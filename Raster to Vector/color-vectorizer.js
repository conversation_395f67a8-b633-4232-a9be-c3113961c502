/**
 * Color-Preserving Image Vectorizer
 * Uses K-means clustering for color quantization and region segmentation
 * to create true vector graphics that preserve the original image appearance
 */

class ColorVectorizer {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.imageData = null;
        this.quantizedColors = [];
        this.colorRegions = [];
    }

    /**
     * Main vectorization function
     * @param {HTMLImageElement} image - Source image
     * @param {Object} options - Vectorization options
     * @returns {string} SVG string
     */
    async vectorize(image, options = {}) {
        const {
            numColors = 12,
            minRegionSize = 50,
            smoothing = 2,
            tolerance = 1.0
        } = options;

        // Step 1: Setup canvas and get image data
        this.setupCanvas(image);
        
        // Step 2: Color quantization using K-means
        console.log('Performing color quantization...');
        await this.quantizeColors(numColors);
        
        // Step 3: Segment image into color regions
        console.log('Segmenting color regions...');
        this.segmentColorRegions(minRegionSize);
        
        // Step 4: Generate SVG paths for each region
        console.log('Generating SVG paths...');
        const svg = this.generateSVG(smoothing, tolerance);
        
        return svg;
    }

    /**
     * Setup canvas and extract image data
     */
    setupCanvas(image) {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        
        this.canvas.width = image.width;
        this.canvas.height = image.height;
        
        this.ctx.drawImage(image, 0, 0);
        this.imageData = this.ctx.getImageData(0, 0, image.width, image.height);
    }

    /**
     * K-means color quantization
     * Reduces image to a small number of dominant colors
     */
    async quantizeColors(numColors) {
        const pixels = [];
        const data = this.imageData.data;
        
        // Extract RGB values (skip alpha for now)
        for (let i = 0; i < data.length; i += 4) {
            pixels.push([data[i], data[i + 1], data[i + 2]]);
        }
        
        // Perform K-means clustering
        this.quantizedColors = await this.kMeansCluster(pixels, numColors);
        
        // Create quantized image
        this.createQuantizedImage();
    }

    /**
     * K-means clustering implementation
     */
    async kMeansCluster(pixels, k, maxIterations = 20) {
        // Initialize centroids randomly
        let centroids = [];
        for (let i = 0; i < k; i++) {
            const randomPixel = pixels[Math.floor(Math.random() * pixels.length)];
            centroids.push([...randomPixel]);
        }
        
        for (let iteration = 0; iteration < maxIterations; iteration++) {
            // Assign pixels to nearest centroid
            const clusters = Array(k).fill().map(() => []);
            
            for (const pixel of pixels) {
                let minDistance = Infinity;
                let closestCentroid = 0;
                
                for (let i = 0; i < centroids.length; i++) {
                    const distance = this.colorDistance(pixel, centroids[i]);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCentroid = i;
                    }
                }
                
                clusters[closestCentroid].push(pixel);
            }
            
            // Update centroids
            const newCentroids = [];
            for (let i = 0; i < k; i++) {
                if (clusters[i].length > 0) {
                    const avgColor = [0, 0, 0];
                    for (const pixel of clusters[i]) {
                        avgColor[0] += pixel[0];
                        avgColor[1] += pixel[1];
                        avgColor[2] += pixel[2];
                    }
                    avgColor[0] = Math.round(avgColor[0] / clusters[i].length);
                    avgColor[1] = Math.round(avgColor[1] / clusters[i].length);
                    avgColor[2] = Math.round(avgColor[2] / clusters[i].length);
                    newCentroids.push(avgColor);
                } else {
                    newCentroids.push(centroids[i]);
                }
            }
            
            // Check for convergence
            let converged = true;
            for (let i = 0; i < k; i++) {
                if (this.colorDistance(centroids[i], newCentroids[i]) > 1) {
                    converged = false;
                    break;
                }
            }
            
            centroids = newCentroids;
            
            if (converged) break;
            
            // Allow UI to update
            if (iteration % 5 === 0) {
                await new Promise(resolve => setTimeout(resolve, 1));
            }
        }
        
        return centroids;
    }

    /**
     * Calculate color distance (Euclidean in RGB space)
     */
    colorDistance(color1, color2) {
        const dr = color1[0] - color2[0];
        const dg = color1[1] - color2[1];
        const db = color1[2] - color2[2];
        return Math.sqrt(dr * dr + dg * dg + db * db);
    }

    /**
     * Create quantized version of the image
     */
    createQuantizedImage() {
        const data = this.imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            const pixel = [data[i], data[i + 1], data[i + 2]];
            
            // Find closest quantized color
            let minDistance = Infinity;
            let closestColor = this.quantizedColors[0];
            
            for (const color of this.quantizedColors) {
                const distance = this.colorDistance(pixel, color);
                if (distance < minDistance) {
                    minDistance = distance;
                    closestColor = color;
                }
            }
            
            // Replace with quantized color
            data[i] = closestColor[0];     // R
            data[i + 1] = closestColor[1]; // G
            data[i + 2] = closestColor[2]; // B
            // Keep original alpha
        }
        
        this.ctx.putImageData(this.imageData, 0, 0);
    }

    /**
     * Segment image into regions of the same color
     */
    segmentColorRegions(minRegionSize) {
        this.colorRegions = [];
        const visited = new Set();
        const width = this.canvas.width;
        const height = this.canvas.height;
        const data = this.imageData.data;
        
        for (const color of this.quantizedColors) {
            const regions = this.findColorRegions(color, data, width, height, visited, minRegionSize);
            this.colorRegions.push(...regions);
        }
    }

    /**
     * Find connected regions of a specific color using flood fill
     */
    findColorRegions(targetColor, data, width, height, visited, minRegionSize) {
        const regions = [];
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = (y * width + x) * 4;
                const key = `${x},${y}`;
                
                if (visited.has(key)) continue;
                
                const pixelColor = [data[index], data[index + 1], data[index + 2]];
                
                if (this.colorDistance(pixelColor, targetColor) < 1) {
                    const region = this.floodFill(x, y, targetColor, data, width, height, visited);
                    
                    if (region.length >= minRegionSize) {
                        regions.push({
                            color: targetColor,
                            pixels: region
                        });
                    }
                }
            }
        }
        
        return regions;
    }

    /**
     * Flood fill algorithm to find connected pixels of the same color
     */
    floodFill(startX, startY, targetColor, data, width, height, visited) {
        const region = [];
        const stack = [[startX, startY]];
        
        while (stack.length > 0) {
            const [x, y] = stack.pop();
            const key = `${x},${y}`;
            
            if (x < 0 || x >= width || y < 0 || y >= height || visited.has(key)) {
                continue;
            }
            
            const index = (y * width + x) * 4;
            const pixelColor = [data[index], data[index + 1], data[index + 2]];
            
            if (this.colorDistance(pixelColor, targetColor) < 1) {
                visited.add(key);
                region.push([x, y]);
                
                // Add neighbors to stack
                stack.push([x + 1, y], [x - 1, y], [x, y + 1], [x, y - 1]);
            }
        }
        
        return region;
    }

    /**
     * Generate SVG from color regions
     */
    generateSVG(smoothing, tolerance) {
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        let svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">\n`;
        
        for (const region of this.colorRegions) {
            const path = this.regionToPath(region, smoothing, tolerance);
            if (path) {
                const colorHex = this.rgbToHex(region.color);
                svg += `  <path d="${path}" fill="${colorHex}" stroke="none"/>\n`;
            }
        }
        
        svg += '</svg>';
        return svg;
    }

    /**
     * Convert region pixels to SVG path
     */
    regionToPath(region, smoothing, tolerance) {
        // Create a binary mask for this region
        const mask = this.createRegionMask(region);
        
        // Find contours in the mask
        const contours = this.findContours(mask);
        
        if (contours.length === 0) return null;
        
        // Convert largest contour to SVG path
        const mainContour = contours.reduce((largest, current) => 
            current.length > largest.length ? current : largest
        );
        
        return this.contourToPath(mainContour, smoothing, tolerance);
    }

    /**
     * Create binary mask for a region
     */
    createRegionMask(region) {
        const width = this.canvas.width;
        const height = this.canvas.height;
        const mask = new Array(height).fill().map(() => new Array(width).fill(0));
        
        for (const [x, y] of region.pixels) {
            mask[y][x] = 1;
        }
        
        return mask;
    }

    /**
     * Find contours in binary mask (simplified implementation)
     */
    findContours(mask) {
        // This is a simplified contour detection
        // In a full implementation, you'd use a more sophisticated algorithm
        const contours = [];
        const height = mask.length;
        const width = mask[0].length;
        const visited = new Set();
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                if (mask[y][x] === 1 && !visited.has(`${x},${y}`)) {
                    const contour = this.traceContour(mask, x, y, visited);
                    if (contour.length > 10) { // Minimum contour size
                        contours.push(contour);
                    }
                }
            }
        }
        
        return contours;
    }

    /**
     * Trace contour boundary
     */
    traceContour(mask, startX, startY, visited) {
        const contour = [];
        const height = mask.length;
        const width = mask[0].length;
        
        // Simple boundary following
        let x = startX, y = startY;
        const directions = [[1,0], [0,1], [-1,0], [0,-1]];
        
        do {
            contour.push([x, y]);
            visited.add(`${x},${y}`);
            
            // Find next boundary point
            let found = false;
            for (const [dx, dy] of directions) {
                const nx = x + dx;
                const ny = y + dy;
                
                if (nx >= 0 && nx < width && ny >= 0 && ny < height && 
                    mask[ny][nx] === 1 && !visited.has(`${nx},${ny}`)) {
                    x = nx;
                    y = ny;
                    found = true;
                    break;
                }
            }
            
            if (!found) break;
            
        } while (contour.length < 1000); // Prevent infinite loops
        
        return contour;
    }

    /**
     * Convert contour points to SVG path
     */
    contourToPath(contour, smoothing, tolerance) {
        if (contour.length < 3) return null;
        
        let path = `M ${contour[0][0]} ${contour[0][1]}`;
        
        for (let i = 1; i < contour.length; i++) {
            path += ` L ${contour[i][0]} ${contour[i][1]}`;
        }
        
        path += ' Z';
        return path;
    }

    /**
     * Convert RGB to hex color
     */
    rgbToHex(rgb) {
        const toHex = (n) => {
            const hex = Math.round(n).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };
        return `#${toHex(rgb[0])}${toHex(rgb[1])}${toHex(rgb[2])}`;
    }
}

// Export for use
window.ColorVectorizer = ColorVectorizer;
