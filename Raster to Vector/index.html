<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color-Preserving Vector Converter</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #999;
            font-size: 0.9em;
        }

        #fileInput {
            display: none;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-group {
            background: rgba(255, 255, 255, 0.7);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }

        .control-group input[type="number"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .range-value {
            text-align: center;
            font-weight: bold;
            color: #667eea;
        }

        .convert-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto 30px;
            min-width: 200px;
        }

        .convert-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .convert-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .preview-section {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .preview-section h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #333;
            font-size: 1.3em;
        }

        .preview-content {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed #e9ecef;
        }

        .preview-content img,
        .preview-content svg {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            margin-top: 15px;
            width: 100%;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #666;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .preview-container {
                grid-template-columns: 1fr;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Color-Preserving Vector Converter</h1>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📁</div>
            <div class="upload-text">Click to upload or drag & drop</div>
            <div class="upload-subtext">Supports PNG, JPG, GIF, BMP</div>
        </div>
        
        <input type="file" id="fileInput" accept="image/*">
        
        <div class="controls">
            <div class="control-group">
                <label for="numColors">Number of Colors</label>
                <input type="range" id="numColors" min="4" max="32" value="12">
                <div class="range-value" id="numColorsValue">12</div>
            </div>
            
            <div class="control-group">
                <label for="minRegionSize">Min Region Size</label>
                <input type="range" id="minRegionSize" min="10" max="200" value="50">
                <div class="range-value" id="minRegionSizeValue">50</div>
            </div>
            
            <div class="control-group">
                <label for="smoothing">Smoothing</label>
                <input type="range" id="smoothing" min="0" max="5" value="2">
                <div class="range-value" id="smoothingValue">2</div>
            </div>
            
            <div class="control-group">
                <label for="tolerance">Path Tolerance</label>
                <input type="range" id="tolerance" min="0.1" max="3" step="0.1" value="1.0">
                <div class="range-value" id="toleranceValue">1.0</div>
            </div>
        </div>
        
        <button class="convert-btn" id="convertBtn" disabled>Convert to Vector</button>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Processing...</div>
        </div>
        
        <div class="preview-container" id="previewContainer" style="display: none;">
            <div class="preview-section">
                <h3>Original</h3>
                <div class="preview-content" id="originalPreview">
                    <span style="color: #999;">No image loaded</span>
                </div>
            </div>
            
            <div class="preview-section">
                <h3>Vectorized</h3>
                <div class="preview-content" id="vectorPreview">
                    <span style="color: #999;">Click convert to see result</span>
                </div>
                <button class="download-btn" id="downloadBtn" style="display: none;">Download SVG</button>
            </div>
        </div>
    </div>

    <script src="color-vectorizer.js"></script>
    <script>
        let currentImage = null;
        let currentSVG = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.querySelector('.upload-area');
        const convertBtn = document.getElementById('convertBtn');
        const progressContainer = document.getElementById('progressContainer');
        const previewContainer = document.getElementById('previewContainer');
        const originalPreview = document.getElementById('originalPreview');
        const vectorPreview = document.getElementById('vectorPreview');
        const downloadBtn = document.getElementById('downloadBtn');

        // Control elements
        const numColors = document.getElementById('numColors');
        const minRegionSize = document.getElementById('minRegionSize');
        const smoothing = document.getElementById('smoothing');
        const tolerance = document.getElementById('tolerance');

        // Update range value displays
        numColors.addEventListener('input', () => {
            document.getElementById('numColorsValue').textContent = numColors.value;
        });

        minRegionSize.addEventListener('input', () => {
            document.getElementById('minRegionSizeValue').textContent = minRegionSize.value;
        });

        smoothing.addEventListener('input', () => {
            document.getElementById('smoothingValue').textContent = smoothing.value;
        });

        tolerance.addEventListener('input', () => {
            document.getElementById('toleranceValue').textContent = tolerance.value;
        });

        // Drag and drop handling
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file.');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    currentImage = img;
                    originalPreview.innerHTML = '';
                    originalPreview.appendChild(img);
                    convertBtn.disabled = false;
                    previewContainer.style.display = 'grid';
                    vectorPreview.innerHTML = '<span style="color: #999;">Click convert to see result</span>';
                    downloadBtn.style.display = 'none';
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // Convert button handling
        convertBtn.addEventListener('click', async () => {
            if (!currentImage) return;

            convertBtn.disabled = true;
            progressContainer.style.display = 'block';

            try {
                const vectorizer = new ColorVectorizer();

                const options = {
                    numColors: parseInt(numColors.value),
                    minRegionSize: parseInt(minRegionSize.value),
                    smoothing: parseInt(smoothing.value),
                    tolerance: parseFloat(tolerance.value)
                };

                // Update progress
                document.getElementById('progressFill').style.width = '20%';
                document.getElementById('progressText').textContent = 'Starting vectorization...';

                const svgString = await vectorizer.vectorize(currentImage, options);

                document.getElementById('progressFill').style.width = '100%';
                document.getElementById('progressText').textContent = 'Complete!';

                currentSVG = svgString;
                vectorPreview.innerHTML = svgString;
                downloadBtn.style.display = 'block';

            } catch (error) {
                console.error('Vectorization failed:', error);
                alert('Vectorization failed. Please try different settings.');
                vectorPreview.innerHTML = '<span style="color: #f00;">Conversion failed</span>';
            } finally {
                convertBtn.disabled = false;
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 1000);
            }
        });

        // Download button handling
        downloadBtn.addEventListener('click', () => {
            if (!currentSVG) return;

            const blob = new Blob([currentSVG], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'vectorized-image.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
    </script>
