// Snap Vectorize Processor - Core vectorization algorithms
// Implements edge detection, path tracing, and SVG generation

class VectorizeProcessor {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.imageData = null;
    this.width = 0;
    this.height = 0;
    this.settings = {
      detailLevel: 50,
      smoothing: 30,
      colorReduction: 16,
      threshold: 128
    };
  }

  // Initialize processor with image
  async processImage(imageFile, settings = {}, progressCallback = null) {
    this.settings = { ...this.settings, ...settings };
    this.progressCallback = progressCallback;

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        try {
          if (this.progressCallback) this.progressCallback({ stage: 'setup', progress: 10 });

          this.setupCanvas(img);

          if (this.progressCallback) this.progressCallback({ stage: 'processing', progress: 20 });

          const result = this.vectorize();

          if (this.progressCallback) this.progressCallback({ stage: 'complete', progress: 100 });

          resolve(result);
        } catch (error) {
          reject(error);
        }
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(imageFile);
    });
  }

  // Setup canvas and get image data
  setupCanvas(img) {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');
    
    // Set canvas size
    this.width = img.width;
    this.height = img.height;
    this.canvas.width = this.width;
    this.canvas.height = this.height;
    
    // Draw image and get pixel data
    this.ctx.drawImage(img, 0, 0);
    this.imageData = this.ctx.getImageData(0, 0, this.width, this.height);
  }

  // Main vectorization process
  vectorize() {
    console.log('Starting vectorization process...');

    // Step 1: Color quantization
    if (this.progressCallback) this.progressCallback({ stage: 'quantizing', progress: 30 });
    const quantizedData = this.quantizeColors(this.imageData);

    // Step 2: Edge detection
    if (this.progressCallback) this.progressCallback({ stage: 'detecting_edges', progress: 50 });
    const edges = this.detectEdges(quantizedData);

    // Step 3: Path tracing
    if (this.progressCallback) this.progressCallback({ stage: 'tracing_paths', progress: 70 });
    const paths = this.tracePaths(edges);

    // Step 4: Path simplification
    if (this.progressCallback) this.progressCallback({ stage: 'simplifying', progress: 85 });
    const simplifiedPaths = this.simplifyPaths(paths);

    // Step 5: Generate SVG
    if (this.progressCallback) this.progressCallback({ stage: 'generating_svg', progress: 95 });
    const svg = this.generateSVG(simplifiedPaths);

    console.log(`Vectorization complete! Generated ${simplifiedPaths.length} paths.`);
    return {
      svg: svg,
      paths: simplifiedPaths,
      preview: this.generatePreview(simplifiedPaths),
      stats: {
        originalSize: { width: this.width, height: this.height },
        pathCount: simplifiedPaths.length,
        totalPoints: simplifiedPaths.reduce((sum, path) => sum + path.length, 0)
      }
    };
  }

  // Color quantization to reduce colors
  quantizeColors(imageData) {
    const data = new Uint8ClampedArray(imageData.data);
    const colorLevels = Math.max(2, Math.min(256, this.settings.colorReduction));
    const step = 256 / colorLevels;
    
    for (let i = 0; i < data.length; i += 4) {
      // Quantize RGB channels
      data[i] = Math.floor(data[i] / step) * step;     // R
      data[i + 1] = Math.floor(data[i + 1] / step) * step; // G
      data[i + 2] = Math.floor(data[i + 2] / step) * step; // B
      // Alpha channel remains unchanged
    }
    
    return new ImageData(data, this.width, this.height);
  }

  // Sobel edge detection
  detectEdges(imageData) {
    const data = imageData.data;
    const edges = new Array(this.width * this.height).fill(0);
    
    // Sobel kernels
    const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
    const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];
    
    // Convert to grayscale and apply Sobel
    for (let y = 1; y < this.height - 1; y++) {
      for (let x = 1; x < this.width - 1; x++) {
        let gx = 0, gy = 0;
        
        // Apply Sobel kernels
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = ((y + ky) * this.width + (x + kx)) * 4;
            const gray = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
            const kernelIdx = (ky + 1) * 3 + (kx + 1);
            
            gx += gray * sobelX[kernelIdx];
            gy += gray * sobelY[kernelIdx];
          }
        }
        
        // Calculate gradient magnitude
        const magnitude = Math.sqrt(gx * gx + gy * gy);
        const threshold = (100 - this.settings.detailLevel) * 2.55; // Convert to 0-255 range
        edges[y * this.width + x] = magnitude > threshold ? 255 : 0;
      }
    }
    
    return edges;
  }

  // Trace paths from edge data
  tracePaths(edges) {
    const visited = new Array(this.width * this.height).fill(false);
    const paths = [];
    
    // Find edge pixels and trace paths
    for (let y = 0; y < this.height; y++) {
      for (let x = 0; x < this.width; x++) {
        const idx = y * this.width + x;
        if (edges[idx] > 0 && !visited[idx]) {
          const path = this.traceContour(edges, visited, x, y);
          if (path.length > 3) { // Only keep paths with sufficient points
            paths.push(path);
          }
        }
      }
    }
    
    return paths;
  }

  // Trace a single contour
  traceContour(edges, visited, startX, startY) {
    const path = [];
    const directions = [
      [-1, -1], [0, -1], [1, -1],
      [-1,  0],          [1,  0],
      [-1,  1], [0,  1], [1,  1]
    ];
    
    let x = startX, y = startY;
    const maxLength = 1000; // Prevent infinite loops
    
    while (path.length < maxLength) {
      const idx = y * this.width + x;
      if (visited[idx]) break;
      
      visited[idx] = true;
      path.push({ x, y });
      
      // Find next edge pixel
      let found = false;
      for (const [dx, dy] of directions) {
        const nx = x + dx;
        const ny = y + dy;
        
        if (nx >= 0 && nx < this.width && ny >= 0 && ny < this.height) {
          const nIdx = ny * this.width + nx;
          if (edges[nIdx] > 0 && !visited[nIdx]) {
            x = nx;
            y = ny;
            found = true;
            break;
          }
        }
      }
      
      if (!found) break;
    }
    
    return path;
  }

  // Simplify paths using Douglas-Peucker algorithm
  simplifyPaths(paths) {
    const tolerance = this.settings.smoothing / 10; // Convert to appropriate scale
    return paths.map(path => this.douglasPeucker(path, tolerance));
  }

  // Douglas-Peucker path simplification
  douglasPeucker(points, tolerance) {
    if (points.length <= 2) return points;
    
    // Find the point with maximum distance from line
    let maxDistance = 0;
    let maxIndex = 0;
    const start = points[0];
    const end = points[points.length - 1];
    
    for (let i = 1; i < points.length - 1; i++) {
      const distance = this.pointToLineDistance(points[i], start, end);
      if (distance > maxDistance) {
        maxDistance = distance;
        maxIndex = i;
      }
    }
    
    // If max distance is greater than tolerance, recursively simplify
    if (maxDistance > tolerance) {
      const left = this.douglasPeucker(points.slice(0, maxIndex + 1), tolerance);
      const right = this.douglasPeucker(points.slice(maxIndex), tolerance);
      return left.slice(0, -1).concat(right);
    } else {
      return [start, end];
    }
  }

  // Calculate distance from point to line
  pointToLineDistance(point, lineStart, lineEnd) {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return Math.sqrt(A * A + B * B);
    
    const param = dot / lenSq;
    let xx, yy;
    
    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }
    
    const dx = point.x - xx;
    const dy = point.y - yy;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // Generate SVG from paths
  generateSVG(paths) {
    const svg = [];
    svg.push(`<svg width="${this.width}" height="${this.height}" viewBox="0 0 ${this.width} ${this.height}" xmlns="http://www.w3.org/2000/svg">`);
    svg.push('<g fill="none" stroke="black" stroke-width="1">');
    
    paths.forEach((path, index) => {
      if (path.length < 2) return;
      
      let pathData = `M ${path[0].x} ${path[0].y}`;
      for (let i = 1; i < path.length; i++) {
        pathData += ` L ${path[i].x} ${path[i].y}`;
      }
      
      svg.push(`<path d="${pathData}" />`);
    });
    
    svg.push('</g>');
    svg.push('</svg>');
    
    return svg.join('\n');
  }

  // Generate preview canvas
  generatePreview(paths) {
    const previewCanvas = document.createElement('canvas');
    previewCanvas.width = this.width;
    previewCanvas.height = this.height;
    const ctx = previewCanvas.getContext('2d');
    
    // Clear canvas
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, this.width, this.height);
    
    // Draw paths
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 1;
    
    paths.forEach(path => {
      if (path.length < 2) return;
      
      ctx.beginPath();
      ctx.moveTo(path[0].x, path[0].y);
      for (let i = 1; i < path.length; i++) {
        ctx.lineTo(path[i].x, path[i].y);
      }
      ctx.stroke();
    });
    
    return previewCanvas;
  }
}

// Export for use in main component
window.VectorizeProcessor = VectorizeProcessor;
