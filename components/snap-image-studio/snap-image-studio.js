// Snap Image Studio Component - Combined HTML, CSS, and JS

// Component-specific CSS as a string
const snapImageStudioCSS = `
/* Snap Image Studio Component Styles */

/* Add tab theme variables */
:root {
  /* Main content padding */
  --main-content-padding: 40px;
  --main-content-padding-mobile: 16px;

  /* Light theme (default) */
  --tab-container-bg: var(--bg-primary);
  --tab-active-bg: #FFFFFF;
  --tab-hover-bg: rgba(255, 255, 255, 0.5);
  --tab-text: #606F95;
  --tab-text-active: #470CED;

  /* Drag & Drop variables */
  --drag-drop-bg: var(--bg-primary);
  --drag-drop-border: #DCE0E5;
  --drag-drop-text: #606F95;
  --drag-drop-hover-border: #470CED;
  --drag-drop-hover-bg: rgba(71, 12, 237, 0.02);
  --drag-drop-active-bg: rgba(71, 12, 237, 0.05);
  --drag-drop-success-border: #00A76F;
  --drag-drop-success-bg: rgba(0, 167, 111, 0.05);
  --drag-drop-error-border: #FF4842;
  --drag-drop-error-bg: rgba(255, 72, 66, 0.05);

  /* Loaded Files UI variables */
  --loaded-files-border: #04AE2C;
  --loaded-files-text: #606F95;
  --loaded-files-counter-bg: rgba(4, 174, 44, 0.1);
  --loaded-files-counter-text: #04AE2C;
  --loaded-files-progress-bg: rgba(96, 111, 149, 0.1);
  --loaded-files-progress-fill: #04AE2C;
  --clear-button-bg: rgba(250, 88, 58, 0.05);
  --clear-button-icon: #FF391F;
  --checkbox-text: #000000;

  /* Checkbox variables */
  --checkbox-size: 20px;
  --checkbox-bg: transparent;
  --checkbox-border: #DCE0E5;
  --checkbox-hover-border: #470CED;
  --checkbox-checked-bg: #470CED;
  --checkbox-checked-border: #470CED;
  --checkbox-icon-color: #FFFFFF;
  --checkbox-unchecked-filter: invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%);
}

[data-theme="dark"] {
  --tab-container-bg: var(--bg-primary); /* align with global bg */
  --tab-active-bg: #292E38;   /* restore default dark active background */
  --tab-hover-bg: rgba(41, 46, 56, 0.5);
  --tab-text: #A1AEBE;
  --tab-text-active: #FFFFFF;

  /* Drag & Drop dark mode */
  --drag-drop-bg: var(--bg-primary);
  --drag-drop-border: #2F353D;
  --drag-drop-text: #FFFFFF;
  --drag-drop-hover-border: #470CED;
  --drag-drop-hover-bg: rgba(71, 12, 237, 0.15);
  --drag-drop-active-bg: rgba(71, 12, 237, 0.2);

  /* Loaded Files UI dark mode */
  --loaded-files-text: #FFFFFF;
  --checkbox-text: #FFFFFF;

  /* Checkbox dark mode */
  --checkbox-border: #2F353D;
  --checkbox-hover-border: #470CED;

  /* Dark theme overrides */
  --tooltip-bg: #000000;
  --tooltip-text: #FFFFFF;
}

/* Notification */
.notification {
  display: inline-block !important;
  width: auto !important;
  position: fixed;
  top: 40px;
  right: 40px;
  max-width: calc(100% - 350px); /* Respect main content margins */
  background-color: var(--tab-active-bg);
  color: var(--tab-text);
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  opacity: 0;
  transform: translateY(-10px);
  animation: notification-show 0.3s ease forwards;
  font-size: 14px;
  border-left: 4px solid #FF9800;
}

/* Handle sidebar collapsed state */
.sidebar.collapsed ~ .main-content .notification {
  max-width: calc(100% - 180px);
}

.notification.warning {
  border-left: 4px solid #FF9800;
}

.notification.success {
  border-left: 4px solid #04AE2C;
}

@keyframes notification-show {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Main content positioning */
.snap-image-studio-component {
  padding: 0 0 20px 0;
  width: 100%;
  min-width: 0;
  position: relative;
  /* height: 100%;  <-- Removed to prevent unnecessary scrollbars */
}

.snap-image-studio-component h1 {
  font-size: 24px;
  margin: 0;
  /* Align with global main-content h1 spacing */
  margin-top: 10px;
  font-weight: 500;
  color: var(--text-accent);
}

.snap-image-studio-content {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  /* height: 100%;  <-- Removed to prevent unnecessary scrollbars */
}

/* Tab Container */
.tab-container {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px;
  background: var(--tab-container-bg);
  border-radius: 14px;
  position: relative;
  /* No transition here */
}

/* Sliding background */
.sliding-background {
  position: absolute;
  height: calc(100% - 10px);
  background: var(--tab-active-bg);
  border-radius: 10px;
  /* No transition here */
  pointer-events: none;
}

.tab {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 240px;
  height: 42px;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  z-index: 1;
  background: transparent;
  padding: 0;
  /* No transition here */
}

.tab span {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: var(--tab-text);
  white-space: nowrap;
  /* No transition here */
}

.tab:not(.active):hover {
  background: var(--tab-hover-bg);
  transition: background 0.2s ease;
}

.tab.active {
  background: var(--tab-active-bg);
}

.tab.active span {
  color: var(--tab-text-active);
}

.tab-icon {
  /* Keep icon transition for color if needed */
  transition: var(--theme-transition);
}

/* Tab Content */
.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.tab-content.hidden {
  display: none;
}

/* Drag & Drop Area */
.drag-drop-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 12px;
  padding: 48px;
  border: 1.5px dashed var(--drag-drop-border);
  border-radius: 6px;
  cursor: pointer;
  background: var(--drag-drop-bg);
  /* No transition here */
}

.drag-drop-area:hover {
  border-color: var(--drag-drop-hover-border);
  background: var(--drag-drop-hover-bg);
  transition: background 0.2s ease, border-color 0.2s ease;
}

.drag-drop-area.drag-active {
  border: 1.5px solid #470CED;
  background: #470CED;
  /* No transition here */
}

.drag-drop-area.drag-active .drag-drop-icon {
  filter: brightness(0) invert(1);
}

.drag-drop-area.drag-active .drag-drop-text,
.drag-drop-area.drag-active .drag-drop-subtext {
  color: #FFFFFF;
}

.drag-drop-icon {
  width: 48px;
  height: 48px;
  /* Keep icon transition for color if needed */
  transition: var(--theme-transition);
}

/* Make drag-drop-icon white in dark mode */
[data-theme="dark"] .drag-drop-icon {
  filter: brightness(0) invert(1);
}

.drag-drop-text {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: var(--drag-drop-text);
  text-align: center;
  margin: 0;
  line-height: 1;
  /* No transition here */
}

.drag-drop-subtext {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: var(--drag-drop-text);
  text-align: center;
  margin: 0;
  line-height: 1;
  /* No transition here */
}

/* Processing States */
.drag-drop-area.processing {
  border-color: var(--drag-drop-hover-border);
  background: var(--drag-drop-active-bg);
  transition: background 0.2s ease, border-color 0.2s ease;
}

.drag-drop-area.success {
  border-color: var(--drag-drop-success-border);
  background: var(--drag-drop-success-bg);
  transition: background 0.2s ease, border-color 0.2s ease;
}

.drag-drop-area.error {
  border-color: var(--drag-drop-error-border);
  background: var(--drag-drop-error-bg);
  transition: background 0.2s ease, border-color 0.2s ease;
}

/* Loaded Files UI */
.loaded-files-container {
  display: none;
  flex-direction: column;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  border: 1.5px dashed var(--loaded-files-border);
  border-radius: 6px;
  background: var(--drag-drop-bg);
  /* Removed transition: var(--theme-transition); */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden !important;
  height: 189px;
  position: relative;
}

.loaded-files-container.visible {
  display: flex;
}

.loaded-files-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 100%;
  gap: 16px;
  margin: 0;
}

.loaded-files-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  min-width: 0;
  margin: 0;
  overflow: hidden !important;
  max-width: calc(100% - 48px) !important;
  position: relative;
}

.loaded-files-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
  margin: 0;
}

.files-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.files-label h3 {
  margin: 0;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.197em;
  color: var(--loaded-files-text);
}

.files-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  height: 17px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 13px;
  font-weight: 700;
  line-height: 1.6em;
  color: #04AE2C;
  white-space: nowrap;
}

.progress-info {
  display: flex;
  align-items: center;
}

.progress-text {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.197em;
  color: var(--loaded-files-text);
  white-space: nowrap;
}

.progress-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0;
  padding: 0;
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 6px !important; /* Force height to be consistent */
  min-height: 6px; /* Ensure minimum height */
  max-height: 6px; /* Ensure maximum height */
  background: var(--loaded-files-progress-bg);
  border-radius: 10px;
  overflow: hidden;
  margin: 0;
  position: relative;
}

/* Ensure progress bar is contained within its parent */
.loaded-files-info .progress-container,
.loaded-files-info .progress-bar {
  max-width: 100%;
  width: 100%;
  display: block;
  box-sizing: border-box;
  overflow: hidden;
}

/* Force progress bar to match parent width exactly */
.loaded-files-info {
  contain: layout;
}

.progress-fill {
  height: 100%;
  background: var(--loaded-files-progress-fill);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 8px;
  border: none;
  border-radius: 40px;
  background: var(--clear-button-bg);
  cursor: pointer;
  transition: var(--theme-transition);
}

.clear-button:hover {
  background: #FF391F !important;
}

.clear-button:hover img {
  filter: brightness(0) invert(1) !important;
}

.clear-button img {
  width: 16px;
  height: 16px;
  color: var(--clear-button-icon);
  transition: var(--theme-transition);
}

.paste-hint {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.197em;
  color: var(--loaded-files-text);
  margin: 0;
}

/* Dark mode adjustments */
[data-theme="dark"] .loaded-files-container {
  border-color: var(--loaded-files-border);
  background: var(--drag-drop-bg);
}

[data-theme="dark"] .files-counter {
  color: #04AE2C;
}

[data-theme="dark"] .clear-button {
  background: var(--clear-button-bg);
}

[data-theme="dark"] .clear-button img {
  color: var(--clear-button-icon);
}

[data-theme="dark"] .progress-bar {
  background: var(--loaded-files-progress-bg);
}

[data-theme="dark"] .progress-fill {
  background: var(--loaded-files-progress-fill);
}

/* Compression Option & Checkbox Styles */
.compression-option {
  display: none; /* Hide by default */
  align-items: center;
  gap: 12px;
  margin: 0;
  overflow: visible;
}

.compression-option label {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--checkbox-text);
  cursor: pointer;
  transition: var(--theme-transition);
  margin: 0;
}

/* Hide action buttons by default */
#startHalftoneButton,
#startCompressionButton {
  display: none;
}

/* Processing Files UI */
.processing-files-container {
  flex-direction: column;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  border: 1.5px solid rgba(96, 111, 149, 0.1);
  border-radius: 6px;
  background: var(--drag-drop-bg);
  /* Removed transition: var(--theme-transition); */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: visible;
  height: 189px;
}

.processing-files-container .progress-bar {
  height: 10px !important;
  min-height: 10px;
  max-height: 10px;
}

.processing-files-container .progress-fill {
  background: linear-gradient(90deg, #04AE2C, #39E5B6);
  background-size: 200% 100%;
  animation: gradient-shift 2s linear infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.processing-files-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 100%;
  gap: 16px;
  margin: 0;
}

.processing-files-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-width: 0;
  margin: 0;
}

.processing-files-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
  margin: 0;
}

.processing-hint {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.197em;
  color: var(--loaded-files-text);
  margin: 0;
}

.processing-button {
  background-color: rgba(255, 57, 31, 0.1) !important; /* 10% opacity red background */
  color: #FF391F !important; /* Red text */
  cursor: pointer;
  display: none; /* Hidden by default */
  transition: background-color 150ms ease, color 150ms ease, transform 150ms ease;
  will-change: background-color, color, transform;
}

.stop-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.processing-button.stopping {
  background-color: rgba(136, 136, 136, 0.1) !important; /* 10% opacity gray */
  color: #888888 !important; /* Gray text */
  cursor: pointer !important;
}

.processing-button:hover:not(.stopping) {
  background-color: #FF391F !important;
  color: #FFFFFF !important;
}

/* Ensure the span inherits the button's text color */
.processing-button span {
  color: inherit !important;
}

/* Global Checkbox Styles */
.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  user-select: none;
}

.checkbox-wrapper img {
  width: 20px;
  height: 20px;
  transition: var(--theme-transition);
}

.checkbox-wrapper img[src*="uncheckedbox-ic"] {
  opacity: 0.2;
}

[data-theme="dark"] .checkbox-wrapper img[src*="uncheckedbox-ic"] {
  opacity: 1;
}

.checkbox-wrapper label {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--checkbox-text);
  cursor: pointer;
  transition: var(--theme-transition);
}

/* Tooltip Styles */
[data-tooltip] {
  position: relative;
}

/* Note: Tooltip positioning now handled by global system in snapapp.js */

/* Ensure proper stacking context */
.compression-options {
  position: relative;
  z-index: 2;
}

.compression-option {
  position: relative;
  z-index: 2;
}

/* Handle tooltip positioning for different screen sizes */
@media screen and (max-width: 1200px) {
  [data-tooltip]:before {
    max-width: 300px;
    white-space: normal;
    text-align: left; /* Align text to the left */
  }
}

/* Tab icon styles */
.tab-icon {
  transition: var(--theme-transition);
}

/* Snap Image Studio tab active overrides: brand background and white text/icon */
.snap-image-studio-component .tab.active {
  background: #470CED;
}

.snap-image-studio-component .tab.active span {
  color: #FFFFFF;
}

.snap-image-studio-component .tab.active .tab-icon {
  filter: brightness(0) invert(1);
}

/* Ensure sliding background matches brand color if present */
.snap-image-studio-component .sliding-background {
  background: #470CED;
}

/* Snap Vectorize Styles */
.vectorize-workspace {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: calc(100vh - 200px);
  min-height: 600px;
}

.vectorize-main-area {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.vectorize-left-panel {
  width: 280px;
  background: var(--tab-active-bg);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  border: 1px solid var(--drag-drop-border);
}

.vectorize-canvas-area {
  flex: 1;
  display: flex;
  gap: 16px;
  min-height: 0;
}

.vectorize-canvas-container {
  flex: 1;
  background: var(--tab-active-bg);
  border-radius: 8px;
  border: 1px solid var(--drag-drop-border);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.vectorize-canvas-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--drag-drop-border);
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: var(--tab-text);
  background: var(--drag-drop-bg);
}

.vectorize-canvas-viewport {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-image:
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

[data-theme="dark"] .vectorize-canvas-viewport {
  background-image:
    linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
    linear-gradient(-45deg, #2a2a2a 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #2a2a2a 75%),
    linear-gradient(-45deg, transparent 75%, #2a2a2a 75%);
}

.vectorize-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid var(--drag-drop-border);
  cursor: grab;
  transition: transform 0.1s ease;
}

.vectorize-canvas:active {
  cursor: grabbing;
}

.vectorize-bottom-panel {
  height: 120px;
  background: var(--tab-active-bg);
  border-radius: 8px;
  border: 1px solid var(--drag-drop-border);
  padding: 16px;
  overflow-x: auto;
  overflow-y: hidden;
}

.vectorize-images-container {
  display: flex;
  gap: 12px;
  height: 100%;
  align-items: center;
}

.vectorize-image-item {
  width: 88px;
  height: 88px;
  border-radius: 6px;
  border: 2px solid var(--drag-drop-border);
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.2s ease, transform 0.2s ease;
  flex-shrink: 0;
  position: relative;
}

.vectorize-image-item:hover {
  border-color: var(--drag-drop-hover-border);
  transform: scale(1.05);
}

.vectorize-image-item.active {
  border-color: #470CED;
  border-width: 3px;
}

.vectorize-image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.vectorize-controls-section {
  border-bottom: 1px solid var(--drag-drop-border);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.vectorize-controls-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.vectorize-section-title {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: var(--tab-text);
  margin-bottom: 12px;
}

.vectorize-control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.vectorize-slider-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vectorize-slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: var(--tab-text);
}

.vectorize-slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--loaded-files-progress-bg);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.vectorize-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #470CED;
  cursor: pointer;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.vectorize-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #470CED;
  cursor: pointer;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.vectorize-zoom-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.vectorize-zoom-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: var(--tab-active-bg);
  color: var(--tab-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  transition: background-color 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.vectorize-zoom-btn:hover {
  background: var(--drag-drop-hover-bg);
}

.vectorize-zoom-level {
  padding: 6px 12px;
  background: var(--tab-active-bg);
  border-radius: 6px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: var(--tab-text);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  min-width: 50px;
  justify-content: center;
}

/* Add spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid var(--loaded-files-progress-fill);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0px;
}

/* Compression Options */
.compression-options {
  display: none; /* Hide by default */
  flex-direction: row;
  flex-wrap: wrap;
  gap: 32px;
  margin: 0;
  overflow: visible;
}

.compression-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.compression-option img {
  width: 20px;
  height: 20px;
  transition: var(--theme-transition);
}

.compression-option img[src*="uncheckedbox-ic"] {
  opacity: 0.2;
}

[data-theme="dark"] .compression-option img[src*="uncheckedbox-ic"] {
  opacity: 1;
}

.compression-option label {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--checkbox-text);
  cursor: pointer;
  transition: var(--theme-transition);
}

/* Only show options and buttons when files are loaded */
.loaded-files-container.visible ~ .compression-options,
.loaded-files-container.visible ~ .compression-option,
.loaded-files-container.visible ~ .action-button:not(.processing-button) {
  display: flex;
}

/* Hide options and buttons when drag-drop is visible */
.drag-drop-area:not([style*="display: none"]) ~ .compression-options,
.drag-drop-area:not([style*="display: none"]) ~ .compression-option,
.drag-drop-area:not([style*="display: none"]) ~ .action-button:not(.processing-button) {
  display: none !important;
}

/* Hide options and buttons when processing is visible */
.processing-files-container:not([style*="display: none"]) ~ .compression-options,
.processing-files-container:not([style*="display: none"]) ~ .compression-option,
.processing-files-container:not([style*="display: none"]) ~ .action-button:not(.processing-button) {
  display: none !important;
}

/* Note: All tooltip functionality now handled by global system in snapapp.js */

/* Snap Image Studio specific override */
.snap-image-studio-component .tab-container {
  display: inline-flex !important;
  align-self: flex-start !important;
  min-width: 0 !important; /* Override global min-width rule */
  width: auto !important; /* Ensure auto width to fit content */
}

/* ------ Fix loaded files container width overflow on narrow viewports ------ */
.snap-image-studio-component .loaded-files-container,
.snap-image-studio-component .processing-files-container {
  min-width: 0 !important; /* Override global 1024px rule */
  width: 100% !important;
}
.snap-image-studio-component .loaded-files-info,
.snap-image-studio-component .progress-container,
.snap-image-studio-component .progress-bar {
  min-width: 0 !important;
  max-width: 100% !important;
}
/* ------------------------------------------------------------------------- */
`;

// Component HTML as a string
const snapImageStudioHTML = `
<div class="snap-image-studio-component">
  <h1>Snap Image Studio</h1>
  <div class="snap-image-studio-content">
    <div class="tab-container">
      <div class="tab active" data-tab="halftone">
        <img src="./assets/halftone-active-ic.svg" alt="Halftone" class="tab-icon" data-active="./assets/halftone-active-ic.svg" data-inactive="./assets/halftone-ic.svg">
        <span>Bulk Halftone Separation</span>
      </div>
      <div class="tab" data-tab="compression">
        <img src="./assets/compression-ic.svg" alt="Compression" class="tab-icon" data-active="./assets/compression-active-ic.svg" data-inactive="./assets/compression-ic.svg">
        <span>Bulk Image Compression</span>
      </div>
      <div class="tab" data-tab="vectorize">
        <img src="./assets/analyse-ic.svg" alt="Vectorize" class="tab-icon" data-active="./assets/analyse-ic.svg" data-inactive="./assets/analyse-ic.svg">
        <span>Snap Vectorize</span>
      </div>
    </div>
    
    <div class="tab-content" id="halftoneContent">
      <!-- Processing Files container - initially hidden -->
      <div class="processing-files-container" id="halftoneProcessingFiles" style="display: none;">
        <div class="processing-files-header">
          <div class="processing-files-info">
            <div class="processing-files-title">
              <div class="files-label">
                <div class="spinner"></div>
                <h3>Processing files</h3>
                <span class="files-counter">1 of 48</span>
              </div>
              <div class="progress-info">
                <span class="progress-text">0%</span>
              </div>
            </div>
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 0%;"></div>
              </div>
            </div>
          </div>
        </div>
        <p class="processing-hint" id="processingHint">Process started. The browser may become temporarily unresponsive during intensive image processing.</p>
      </div>

      <div class="loaded-files-container" id="halftoneLoadedFiles">
        <div class="loaded-files-header">
          <div class="loaded-files-info">
            <div class="loaded-files-title">
              <div class="files-label">
                <h3>Loaded files</h3>
                <span class="files-counter">48 of 100</span>
              </div>
              <div class="progress-info">
                <span class="progress-text">89% of max files</span>
              </div>
            </div>
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 89%;"></div>
              </div>
            </div>
          </div>
          <button class="clear-button" data-tooltip="Clear files">
            <img src="./assets/clear.svg" alt="Clear">
          </button>
        </div>
        <p class="paste-hint">Paste (Ctrl/CMD+V) to add more files</p>
      </div>

      <div class="drag-drop-area" id="halftoneUploadArea">
        <img src="./assets/drag-drop-multiple-ic.svg" alt="Drag and Drop" class="drag-drop-icon">
        <p class="drag-drop-text">Drag and drop a folder or multiple images here, or paste (Ctrl/CMD+V).</p>
        <p class="drag-drop-subtext">Upload up to 100 files in PNG, JPG, or WebP format.</p>
        <input type="file" id="halftoneFileInput" multiple accept="image/*" style="display: none;">
      </div>

      <div class="compression-option">
        <div class="checkbox-wrapper" id="compressionCheckbox" data-tooltip="Reduces file size below 20MB. This may slow down the process.">
          <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" class="checkbox-icon">
          <label>Apply adaptive compression to the final image</label>
        </div>
      </div>

      <button class="action-button full-width" id="startHalftoneButton">
        <img src="./assets/apply.svg" alt="Start">
        <span>Start Bulk Halftone Separation</span>
      </button>

      <!-- Processing button - initially hidden -->
      <button class="action-button full-width processing-button" id="processingHalftoneButton" style="display: none;">
        <div class="stop-content">
          <span>Stop Processing</span>
        </div>
      </button>
    </div>
    
    <div class="tab-content hidden" id="compressionContent">
      <!-- Processing Files container - initially hidden -->
      <div class="processing-files-container" id="compressionProcessingFiles" style="display: none;">
        <div class="processing-files-header">
          <div class="processing-files-info">
            <div class="processing-files-title">
              <div class="files-label">
                <div class="spinner"></div>
                <h3>Processing files</h3>
                <span class="files-counter">1 of 48</span>
              </div>
              <div class="progress-info">
                <span class="progress-text">0%</span>
              </div>
            </div>
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 0%;"></div>
              </div>
            </div>
          </div>
        </div>
        <p class="processing-hint" id="compressionProcessingHint">Process started. The browser may become temporarily unresponsive during intensive image processing.</p>
      </div>

      <div class="loaded-files-container" id="compressionLoadedFiles">
        <div class="loaded-files-header">
          <div class="loaded-files-info">
            <div class="loaded-files-title">
              <div class="files-label">
                <h3>Loaded files</h3>
                <span class="files-counter">48 of 100</span>
              </div>
              <div class="progress-info">
                <span class="progress-text">89% of max files</span>
              </div>
            </div>
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 89%;"></div>
              </div>
            </div>
          </div>
          <button class="clear-button" data-tooltip="Clear files">
            <img src="./assets/clear.svg" alt="Clear">
          </button>
        </div>
        <p class="paste-hint">Paste (Ctrl/CMD+V) to add more files</p>
      </div>

      <div class="drag-drop-area" id="compressionUploadArea">
        <img src="./assets/drag-drop-multiple-ic.svg" alt="Drag and Drop" class="drag-drop-icon">
        <p class="drag-drop-text">Drag and drop a folder or multiple images here, or paste (Ctrl/CMD+V).</p>
        <p class="drag-drop-subtext">Upload up to 100 files in PNG format.</p>
        <input type="file" id="compressionFileInput" multiple accept="image/png" style="display: none;">
      </div>

      <div class="compression-options">
        <div class="compression-option" id="adaptiveCompressionCheckbox" data-tooltip="Automatically adjusts compression to keep file size under 20MB">
          <img src="./assets/checkbox-ic.svg" alt="Checkbox">
          <label>Adaptive Mode</label>
        </div>
        <div class="compression-option" id="lightCompressionCheckbox" data-tooltip="Light compression - best quality, minimal size reduction">
          <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox">
          <label>Light Mode</label>
        </div>
        <div class="compression-option" id="balancedCompressionCheckbox" data-tooltip="Balanced compression - good balance of quality and size">
          <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox">
          <label>Balanced Mode</label>
        </div>
        <div class="compression-option" id="aggressiveCompressionCheckbox" data-tooltip="Aggressive compression - maximum size reduction">
          <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox">
          <label>Aggressive Mode</label>
        </div>
      </div>

      <button class="action-button full-width" id="startCompressionButton" style="display: none;">
        <img src="./assets/apply.svg" alt="Start">
        <span>Start Bulk Image Compression</span>
      </button>

      <!-- Processing button - initially hidden -->
      <button class="action-button full-width processing-button" id="processingCompressionButton" style="display: none;">
        <div class="stop-content">
          <span>Stop Processing</span>
        </div>
      </button>
    </div>

    <div class="tab-content hidden" id="vectorizeContent">
      <div class="vectorize-workspace">
        <!-- Drag and Drop Area (initially visible) -->
        <div class="drag-drop-area" id="vectorizeUploadArea">
          <img src="./assets/drag-drop-multiple-ic.svg" alt="Drag and Drop" class="drag-drop-icon">
          <p class="drag-drop-text">Drag and drop images here to start vectorizing, or paste (Ctrl/CMD+V).</p>
          <p class="drag-drop-subtext">Upload images in PNG, JPG, or WebP format.</p>
          <input type="file" id="vectorizeFileInput" multiple accept="image/*" style="display: none;">
        </div>

        <!-- Vectorize Interface (initially hidden) -->
        <div class="vectorize-main-area" id="vectorizeInterface" style="display: none;">
          <!-- Left Panel - Controls -->
          <div class="vectorize-left-panel">
            <div class="vectorize-controls-section">
              <h3 class="vectorize-section-title">Vectorization Settings</h3>
              <div class="vectorize-control-group">
                <div class="vectorize-slider-container">
                  <div class="vectorize-slider-label">
                    <span>Detail Level</span>
                    <span id="detailValue">50</span>
                  </div>
                  <input type="range" class="vectorize-slider" id="detailSlider" min="1" max="100" value="50">
                </div>

                <div class="vectorize-slider-container">
                  <div class="vectorize-slider-label">
                    <span>Smoothing</span>
                    <span id="smoothingValue">30</span>
                  </div>
                  <input type="range" class="vectorize-slider" id="smoothingSlider" min="0" max="100" value="30">
                </div>

                <div class="vectorize-slider-container">
                  <div class="vectorize-slider-label">
                    <span>Color Reduction</span>
                    <span id="colorValue">16</span>
                  </div>
                  <input type="range" class="vectorize-slider" id="colorSlider" min="2" max="256" value="16">
                </div>
              </div>
            </div>

            <div class="vectorize-controls-section">
              <h3 class="vectorize-section-title">Actions</h3>
              <div class="vectorize-control-group">
                <button class="action-button full-width" id="startVectorizeButton">
                  <img src="./assets/apply.svg" alt="Vectorize">
                  <span>Start Vectorization</span>
                </button>

                <button class="action-button full-width" id="exportSvgButton" style="display: none;">
                  <img src="./assets/apply.svg" alt="Export">
                  <span>Export to SVG</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Canvas Area -->
          <div class="vectorize-canvas-area">
            <!-- Original Image Canvas -->
            <div class="vectorize-canvas-container">
              <div class="vectorize-canvas-header">Original</div>
              <div class="vectorize-canvas-viewport" id="originalViewport">
                <canvas id="originalCanvas" class="vectorize-canvas"></canvas>
                <div class="vectorize-zoom-controls">
                  <button class="vectorize-zoom-btn" id="zoomOut">−</button>
                  <div class="vectorize-zoom-level" id="zoomLevel">100%</div>
                  <button class="vectorize-zoom-btn" id="zoomIn">+</button>
                </div>
              </div>
            </div>

            <!-- Vectorized Image Canvas -->
            <div class="vectorize-canvas-container">
              <div class="vectorize-canvas-header">Vectorized</div>
              <div class="vectorize-canvas-viewport" id="vectorizedViewport">
                <canvas id="vectorizedCanvas" class="vectorize-canvas"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Bottom Panel - Image Gallery -->
        <div class="vectorize-bottom-panel" id="vectorizeBottomPanel" style="display: none;">
          <div class="vectorize-images-container" id="vectorizeImagesContainer">
            <!-- Images will be dynamically added here -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

// Add pako.min.js script
function loadPakoScript() {
  return new Promise((resolve, reject) => {
    if (window.pako) {
      resolve();
      return;
    }
    
    // Check if script already exists
    if (document.querySelector('script[src="./pako.min.js"]')) {
      const checkInterval = setInterval(() => {
        if (window.pako) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      return;
    }
    
    const script = document.createElement('script');
    script.src = './pako.min.js';
    script.async = true;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load pako.min.js'));
    document.head.appendChild(script);
  });
}

// Add UPNG.js script
function loadUPNGScript() {
  return new Promise((resolve, reject) => {
    if (window.UPNG) {
      resolve();
      return;
    }
    
    // Check if script already exists
    if (document.querySelector('script[src="./UPNG.js"]')) {
      const checkInterval = setInterval(() => {
        if (window.UPNG) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      return;
    }
    
    const script = document.createElement('script');
    script.src = './UPNG.js';
    script.async = true;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load UPNG.js'));
    document.head.appendChild(script);
  });
}

// Add halftone processor script
function loadHalftoneProcessorScript() {
  return new Promise((resolve, reject) => {
    if (window.halftoneProcessor) {
      resolve();
      return;
    }

    // Check if script already exists
    if (document.querySelector('script[src="./components/snap-image-studio/halftone-processor.js"]')) {
      const checkInterval = setInterval(() => {
        if (window.halftoneProcessor) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      return;
    }

    const script = document.createElement('script');
    script.src = './components/snap-image-studio/halftone-processor.js';
    script.async = true;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load halftone-processor.js'));
    document.head.appendChild(script);
  });
}

// Add vectorize processor script
function loadVectorizeProcessorScript() {
  return new Promise((resolve, reject) => {
    if (window.VectorizeProcessor) {
      resolve();
      return;
    }

    // Check if script already exists
    if (document.querySelector('script[src="./components/snap-image-studio/vectorize-processor.js"]')) {
      const checkInterval = setInterval(() => {
        if (window.VectorizeProcessor) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      return;
    }

    const script = document.createElement('script');
    script.src = './components/snap-image-studio/vectorize-processor.js';
    script.async = true;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load vectorize-processor.js'));
    document.head.appendChild(script);
  });
}

// Add compression processor functionality
window.compressionProcessor = {
  // Process image with specified compression level
  async processImage(file, compressionLevel, progressCallback) {
    try {
      // Convert file to arrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      
      // Decode PNG using UPNG
      const pngData = UPNG.decode(arrayBuffer);
      
      // Get RGBA8 data
      const rgba8Data = UPNG.toRGBA8(pngData)[0];
      
      // Update progress
      if (progressCallback) {
        progressCallback({ stage: 'preparing', progress: 30 });
      }
      
      // Get compression level based on mode
      let level;
      switch(compressionLevel) {
        case 'light':
          level = 1024; // Light compression
          break;
        case 'balanced':
          level = 512; // Balanced compression
          break;
        case 'aggressive':
          level = 128; // Maximum compression
          break;
        case 'adaptive':
          // Start with high compression for large files
          level = file.size > 20 * 1024 * 1024 ? 128 : 512;
          break;
        default:
          level = 512; // Default to balanced
      }
      
      // Update progress
      if (progressCallback) {
        progressCallback({ stage: 'compressing', progress: 50 });
      }
      
      // Encode with compression
      const compressedData = UPNG.encode([rgba8Data], pngData.width, pngData.height, level);
      
      // Update progress
      if (progressCallback) {
        progressCallback({ stage: 'finalizing', progress: 90 });
      }
      
      // Create blob from compressed data
      const blob = new Blob([compressedData], { type: 'image/png' });
      
      // Final progress update
      if (progressCallback) {
        progressCallback({ stage: 'complete', progress: 100 });
      }
      
      return blob;
    } catch (error) {
      console.error('Error processing image:', error);
      throw error;
    }
  },

  // Get compression settings based on level
  getCompressionSettings(level, fileSize) {
    const settings = {
      'adaptive': {
        ctype: 0, // 0 for auto color type
        filter: -1, // -1 for auto filter
        // Adjust compression based on file size
        level: fileSize > 20 * 1024 * 1024 ? 9 : 6
      },
      'light': {
        ctype: 0,
        filter: -1,
        level: 3 // Light compression
      },
      'balanced': {
        ctype: 0,
        filter: -1,
        level: 6 // Balanced compression
      },
      'aggressive': {
        ctype: 0,
        filter: -1,
        level: 9 // Maximum compression
      }
    };

    return settings[level] || settings.balanced;
  }
};

// Inject component CSS if not already injected
if (!document.getElementById('snap-image-studio-component-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'snap-image-studio-component-styles';
  styleElement.textContent = snapImageStudioCSS;
  document.head.appendChild(styleElement);
}

// Component JS logic
async function initSnapImageStudio() {
  console.log('Snap Image Studio component loaded - start initialization');

  // Initialize Vectorize Interface
  function initializeVectorizeInterface() {
    // Get elements
    const detailSlider = document.getElementById('detailSlider');
    const smoothingSlider = document.getElementById('smoothingSlider');
    const colorSlider = document.getElementById('colorSlider');
    const detailValue = document.getElementById('detailValue');
    const smoothingValue = document.getElementById('smoothingValue');
    const colorValue = document.getElementById('colorValue');
    const startVectorizeButton = document.getElementById('startVectorizeButton');
    const exportSvgButton = document.getElementById('exportSvgButton');
    const zoomInBtn = document.getElementById('zoomIn');
    const zoomOutBtn = document.getElementById('zoomOut');
    const zoomLevel = document.getElementById('zoomLevel');

    // Slider event listeners with real-time preview
    if (detailSlider && detailValue) {
      detailSlider.addEventListener('input', (e) => {
        detailValue.textContent = e.target.value;
        debounceVectorizePreview();
      });
    }

    if (smoothingSlider && smoothingValue) {
      smoothingSlider.addEventListener('input', (e) => {
        smoothingValue.textContent = e.target.value;
        debounceVectorizePreview();
      });
    }

    if (colorSlider && colorValue) {
      colorSlider.addEventListener('input', (e) => {
        colorValue.textContent = e.target.value;
        debounceVectorizePreview();
      });
    }

    // Zoom controls
    if (zoomInBtn) {
      zoomInBtn.addEventListener('click', () => {
        vectorizeZoom = Math.min(vectorizeZoom * 1.2, 5);
        updateCanvasTransform();
        updateZoomDisplay();
      });
    }

    if (zoomOutBtn) {
      zoomOutBtn.addEventListener('click', () => {
        vectorizeZoom = Math.max(vectorizeZoom / 1.2, 0.1);
        updateCanvasTransform();
        updateZoomDisplay();
      });
    }

    // Canvas pan functionality
    initializeCanvasPanning();

    // Action buttons
    if (startVectorizeButton) {
      startVectorizeButton.addEventListener('click', startVectorization);
    }

    if (exportSvgButton) {
      exportSvgButton.addEventListener('click', exportToSVG);
    }
  }

  function showVectorizeInterface() {
    const uploadArea = document.getElementById('vectorizeUploadArea');
    const vectorizeInterface = document.getElementById('vectorizeInterface');
    const bottomPanel = document.getElementById('vectorizeBottomPanel');

    if (uploadArea) uploadArea.style.display = 'none';
    if (vectorizeInterface) vectorizeInterface.style.display = 'flex';
    if (bottomPanel) bottomPanel.style.display = 'block';
  }

  function loadImageToVectorizeCanvas(file) {
    const originalCanvas = document.getElementById('originalCanvas');
    const ctx = originalCanvas.getContext('2d');

    const img = new Image();
    img.onload = function() {
      // Set canvas size
      const maxSize = 400;
      let { width, height } = img;

      if (width > height) {
        if (width > maxSize) {
          height = (height * maxSize) / width;
          width = maxSize;
        }
      } else {
        if (height > maxSize) {
          width = (width * maxSize) / height;
          height = maxSize;
        }
      }

      originalCanvas.width = width;
      originalCanvas.height = height;

      // Draw image
      ctx.drawImage(img, 0, 0, width, height);

      // Store current image
      currentVectorizeImage = { file, img, canvas: originalCanvas };

      // Update bottom panel with loaded images
      updateVectorizeBottomPanel();

      // Reset zoom and pan
      vectorizeZoom = 1;
      vectorizePanX = 0;
      vectorizePanY = 0;
      updateCanvasTransform();
      updateZoomDisplay();
    };

    img.src = URL.createObjectURL(file);
  }

  function updateVectorizeBottomPanel() {
    const container = document.getElementById('vectorizeImagesContainer');
    if (!container) return;

    container.innerHTML = '';

    loadedFiles.vectorize.forEach((file, index) => {
      const imageItem = document.createElement('div');
      imageItem.className = 'vectorize-image-item';
      if (index === 0) imageItem.classList.add('active');

      const img = document.createElement('img');
      img.src = URL.createObjectURL(file);
      img.alt = file.name;

      imageItem.appendChild(img);
      imageItem.addEventListener('click', () => {
        // Remove active class from all items
        container.querySelectorAll('.vectorize-image-item').forEach(item => {
          item.classList.remove('active');
        });
        // Add active class to clicked item
        imageItem.classList.add('active');
        // Load this image to canvas
        loadImageToVectorizeCanvas(file);
      });

      container.appendChild(imageItem);
    });
  }

  function initializeCanvasPanning() {
    const originalViewport = document.getElementById('originalViewport');
    const vectorizedViewport = document.getElementById('vectorizedViewport');

    [originalViewport, vectorizedViewport].forEach(viewport => {
      if (!viewport) return;

      viewport.addEventListener('mousedown', (e) => {
        isDragging = true;
        lastMouseX = e.clientX;
        lastMouseY = e.clientY;
        viewport.style.cursor = 'grabbing';
      });

      viewport.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        const deltaX = e.clientX - lastMouseX;
        const deltaY = e.clientY - lastMouseY;

        vectorizePanX += deltaX;
        vectorizePanY += deltaY;

        lastMouseX = e.clientX;
        lastMouseY = e.clientY;

        updateCanvasTransform();
      });

      viewport.addEventListener('mouseup', () => {
        isDragging = false;
        viewport.style.cursor = 'grab';
      });

      viewport.addEventListener('mouseleave', () => {
        isDragging = false;
        viewport.style.cursor = 'grab';
      });

      // Wheel zoom
      viewport.addEventListener('wheel', (e) => {
        e.preventDefault();
        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        vectorizeZoom = Math.max(0.1, Math.min(5, vectorizeZoom * zoomFactor));
        updateCanvasTransform();
        updateZoomDisplay();
      });
    });
  }

  function updateCanvasTransform() {
    const originalCanvas = document.getElementById('originalCanvas');
    const vectorizedCanvas = document.getElementById('vectorizedCanvas');

    const transform = `translate(calc(-50% + ${vectorizePanX}px), calc(-50% + ${vectorizePanY}px)) scale(${vectorizeZoom})`;

    if (originalCanvas) {
      originalCanvas.style.transform = transform;
    }
    if (vectorizedCanvas) {
      vectorizedCanvas.style.transform = transform;
    }
  }

  function updateZoomDisplay() {
    const zoomLevelElement = document.getElementById('zoomLevel');
    if (zoomLevelElement) {
      zoomLevelElement.textContent = `${Math.round(vectorizeZoom * 100)}%`;
    }
  }

  async function startVectorization() {
    if (!currentVectorizeImage) {
      showNotification('Please load an image first', 'warning');
      return;
    }

    if (!window.VectorizeProcessor) {
      showNotification('Vectorization processor not loaded. Please refresh and try again.', 'error');
      return;
    }

    try {
      // Disable button during processing
      const startButton = document.getElementById('startVectorizeButton');
      if (startButton) {
        startButton.disabled = true;
        startButton.innerHTML = '<span>Processing...</span>';
      }

      showNotification('Starting vectorization...', 'success');

      // Get current settings
      const settings = {
        detailLevel: parseInt(document.getElementById('detailSlider')?.value || 50),
        smoothing: parseInt(document.getElementById('smoothingSlider')?.value || 30),
        colorReduction: parseInt(document.getElementById('colorSlider')?.value || 16)
      };

      // Initialize processor
      vectorizeProcessor = new window.VectorizeProcessor();

      // Process the image
      currentVectorizeResult = await vectorizeProcessor.processImage(currentVectorizeImage.file, settings);

      // Display result on vectorized canvas
      displayVectorizedResult(currentVectorizeResult);

      // Show export button
      const exportButton = document.getElementById('exportSvgButton');
      if (exportButton) {
        exportButton.style.display = 'flex';
      }

      showNotification('Vectorization completed successfully!', 'success');

    } catch (error) {
      console.error('Vectorization failed:', error);
      showNotification('Vectorization failed: ' + error.message, 'error');
    } finally {
      // Re-enable button
      const startButton = document.getElementById('startVectorizeButton');
      if (startButton) {
        startButton.disabled = false;
        startButton.innerHTML = '<img src="./assets/apply.svg" alt="Vectorize"><span>Start Vectorization</span>';
      }
    }
  }

  function displayVectorizedResult(result) {
    const vectorizedCanvas = document.getElementById('vectorizedCanvas');
    const vectorizedCtx = vectorizedCanvas.getContext('2d');

    if (!result || !result.preview) {
      showNotification('Failed to generate vectorized preview', 'error');
      return;
    }

    // Set canvas size to match original
    const originalCanvas = document.getElementById('originalCanvas');
    vectorizedCanvas.width = originalCanvas.width;
    vectorizedCanvas.height = originalCanvas.height;

    // Clear canvas
    vectorizedCtx.fillStyle = 'white';
    vectorizedCtx.fillRect(0, 0, vectorizedCanvas.width, vectorizedCanvas.height);

    // Draw the vectorized preview
    vectorizedCtx.drawImage(result.preview, 0, 0);
  }

  function exportToSVG() {
    if (!currentVectorizeResult || !currentVectorizeResult.svg) {
      showNotification('No vectorized image to export. Please run vectorization first.', 'warning');
      return;
    }

    try {
      // Create blob from SVG string
      const svgBlob = new Blob([currentVectorizeResult.svg], { type: 'image/svg+xml' });

      // Create download link
      const url = URL.createObjectURL(svgBlob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename
      const originalName = currentVectorizeImage.file.name;
      const baseName = originalName.substring(0, originalName.lastIndexOf('.')) || originalName;
      link.download = `${baseName}_vectorized.svg`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      showNotification('SVG exported successfully!', 'success');

    } catch (error) {
      console.error('SVG export failed:', error);
      showNotification('Failed to export SVG: ' + error.message, 'error');
    }
  }

  // Debounced preview update for real-time slider changes
  function debounceVectorizePreview() {
    if (!currentVectorizeImage || !window.VectorizeProcessor) return;

    // Clear existing timeout
    if (previewTimeout) {
      clearTimeout(previewTimeout);
    }

    // Set new timeout for preview update
    previewTimeout = setTimeout(async () => {
      try {
        // Get current settings
        const settings = {
          detailLevel: parseInt(document.getElementById('detailSlider')?.value || 50),
          smoothing: parseInt(document.getElementById('smoothingSlider')?.value || 30),
          colorReduction: parseInt(document.getElementById('colorSlider')?.value || 16)
        };

        // Quick preview with lower quality for responsiveness
        const quickProcessor = new window.VectorizeProcessor();
        const quickResult = await quickProcessor.processImage(currentVectorizeImage.file, {
          ...settings,
          detailLevel: Math.max(10, settings.detailLevel - 20) // Lower detail for speed
        });

        // Update vectorized canvas
        displayVectorizedResult(quickResult);

      } catch (error) {
        console.warn('Preview update failed:', error);
      }
    }, 500); // 500ms delay for responsiveness
  }

  // Set HTML content
  const mainContent = document.querySelector('.main-content');
  if (mainContent) {
    console.log('Found main-content element, setting snap-image-studio HTML');
    mainContent.innerHTML = snapImageStudioHTML;
  } else {
    console.error('Main content element not found when initializing snap-image-studio');
    return; // Exit if we can't find the main content element
  }
  
  // Load required scripts
  try {
    await Promise.all([
      loadPakoScript(),
      loadUPNGScript(),
      loadHalftoneProcessorScript(),
      loadVectorizeProcessorScript()
    ]);
    console.log('Required scripts loaded successfully');
  } catch (error) {
    console.error('Failed to load required scripts:', error);
    showNotification('Failed to load required scripts. Please refresh and try again.', 'error');
    return;
  }
  
  // Store loaded files
  let loadedFiles = {
    halftone: [],
    compression: [],
    vectorize: []
  };
  
  // Track processing state
  let isProcessing = false;
  let shouldStopProcessing = false;
  let selectedSaveLocation = null;
  let applyCompression = false; // Initialize compression flag
  let currentAbortController = null; // Abort controller for cooperative cancellation

  // Vectorize state variables
  let currentVectorizeImage = null;
  let vectorizeZoom = 1;
  let vectorizePanX = 0;
  let vectorizePanY = 0;
  let isDragging = false;
  let lastMouseX = 0;
  let lastMouseY = 0;
  let vectorizeProcessor = null;
  let currentVectorizeResult = null;
  let previewTimeout = null;
  
  // Initialize checkbox states
  function setupCompressionCheckboxes() {
    // For halftone tab
    const compressionCheckbox = document.getElementById('compressionCheckbox');
    if (compressionCheckbox) {
      // Get all clickable elements
      const checkboxIcon = compressionCheckbox.querySelector('.checkbox-icon');
      const checkboxLabel = compressionCheckbox.querySelector('label');
      
      if (!checkboxIcon) return;
      
      // Ensure initial state matches variable
      checkboxIcon.src = `./assets/${applyCompression ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
      
      // Function to toggle checkbox state
      const toggleCheckbox = (event) => {
        // Prevent any default behavior
        event.preventDefault();
        event.stopPropagation();
        
        // Toggle checkbox state
        applyCompression = !applyCompression;
        
        // Update icon
        checkboxIcon.src = `./assets/${applyCompression ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
        console.log('Compression checkbox toggled:', applyCompression);
      };
      
      // Add event listeners to all parts of the checkbox
      compressionCheckbox.addEventListener('click', toggleCheckbox);
      
      // Also attach to child elements directly to ensure bubbling works properly
      if (checkboxIcon) checkboxIcon.addEventListener('click', toggleCheckbox);
      if (checkboxLabel) checkboxLabel.addEventListener('click', toggleCheckbox);
    }
    
    // For compression tab
    const compressionOptions = [
      { id: 'adaptiveCompressionCheckbox', level: 'adaptive' },
      { id: 'lightCompressionCheckbox', level: 'light' },
      { id: 'balancedCompressionCheckbox', level: 'balanced' },
      { id: 'aggressiveCompressionCheckbox', level: 'aggressive' }
    ];
    
    let selectedCompressionLevel = 'adaptive';
    
    compressionOptions.forEach(option => {
      const checkbox = document.getElementById(option.id);
      if (!checkbox) return;
      
      const checkboxIcon = checkbox.querySelector('img');
      const checkboxLabel = checkbox.querySelector('label');
      
      // Set initial state
      checkboxIcon.src = `./assets/${option.level === selectedCompressionLevel ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
      
      const toggleCheckbox = (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        // Only proceed if this isn't already selected
        if (option.level !== selectedCompressionLevel) {
          // Update all checkboxes
          compressionOptions.forEach(opt => {
            const cb = document.getElementById(opt.id);
            if (cb) {
              const icon = cb.querySelector('img');
              if (icon) {
                icon.src = `./assets/${opt.level === option.level ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
              }
            }
          });
          
          selectedCompressionLevel = option.level;
          console.log('Compression level selected:', selectedCompressionLevel);
        }
      };
      
      // Add event listeners
      checkbox.addEventListener('click', toggleCheckbox);
      if (checkboxIcon) checkboxIcon.addEventListener('click', toggleCheckbox);
      if (checkboxLabel) checkboxLabel.addEventListener('click', toggleCheckbox);
    });
    
    // For debugging
    console.log('Compression checkboxes setup complete');
    
    // Return the function to get current compression level
    return () => selectedCompressionLevel;
  }
  
  // Set up UI with a small delay to ensure DOM is fully ready
  setTimeout(() => {
    // Initialize checkboxes and get compression level getter
    const getSelectedCompressionLevel = setupCompressionCheckboxes();

    // Initialize tabs and other UI components
    initializeTabs();
    initializeDragAndDrop();
    initializeClipboardHandling();
    initializeActionButtons(getSelectedCompressionLevel);
    initializeVectorizeInterface();

    console.log('UI initialization complete');
  }, 100);
  
  // Initialize tabs
  function initializeTabs() {
    const tabs = document.querySelectorAll('.tab');
    const slidingBackground = document.querySelector('.sliding-background');

    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs
        tabs.forEach(t => t.classList.remove('active'));
        
        // Add active class to clicked tab
        tab.classList.add('active');
        
        // Update sliding background position
        if (slidingBackground) {
          slidingBackground.style.width = tab.offsetWidth + 'px';
          slidingBackground.style.transform = `translateX(${tab.offsetLeft}px)`;
        }
        
        // Update tab icons
        updateTabIcons();
        
        // Show or hide areas based on selected tab
        const selectedTab = tab.getAttribute('data-tab');
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.toggle('hidden', content.id !== selectedTab + 'Content');
        });
        
        // Show tip card only for halftone tab
        if (selectedTab === 'halftone') {
          showTipCard();
        } else {
          hideTipCard();
        }

        // Handle vectorize tab specific logic
        if (selectedTab === 'vectorize' && loadedFiles.vectorize.length > 0) {
          showVectorizeInterface();
        }
      });
    });

    // Set initial state
    const firstTab = tabs[0];
    if (firstTab) {
      firstTab.click();
    }
  }
  
  // Update tab icons based on active state
  function updateTabIcons() {
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
      const icon = tab.querySelector('.tab-icon');
      if (icon) {
        const activeIconSrc = icon.getAttribute('data-active');
        const inactiveIconSrc = icon.getAttribute('data-inactive');
        
        if (tab.classList.contains('active') && activeIconSrc) {
          icon.src = activeIconSrc;
        } else if (inactiveIconSrc) {
          icon.src = inactiveIconSrc;
        }
      }
    });
  }
  
  // Initialize drag and drop
  function initializeDragAndDrop() {
    const dropAreas = document.querySelectorAll('.drag-drop-area');
    
    dropAreas.forEach(area => {
      const input = area.querySelector('input[type="file"]');
      
      // Click on area to trigger file input
      area.addEventListener('click', () => {
        if (input) input.click();
      });
      
      // Handle file selection via input
      if (input) {
        input.addEventListener('change', (e) => {
          if (e.target.files.length > 0) {
            processFiles(area, e.target.files);
          }
        });
      }
      
      // Drag events
      area.addEventListener('dragover', (e) => {
        e.preventDefault();
        area.classList.add('drag-active');
      });
      
      area.addEventListener('dragleave', () => {
        area.classList.remove('drag-active');
      });
      
      area.addEventListener('drop', (e) => {
        e.preventDefault();
        area.classList.remove('drag-active');
        
        if (e.dataTransfer.items) {
          processFiles(area, e.dataTransfer.files);
        }
      });
    });
  }
  
  // Initialize clipboard paste handling
  function initializeClipboardHandling() {
    document.addEventListener('paste', (e) => {
      const items = e.clipboardData.items;
      const imageItems = [];
      
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          imageItems.push(items[i].getAsFile());
        }
      }
      
      if (imageItems.length > 0) {
        // Get active tab
        const activeTab = document.querySelector('.tab.active');
        if (!activeTab) return;
        
        const tabId = activeTab.getAttribute('data-tab');
        
        // Process pasted files for the active tab
        processFiles(document.getElementById(`${tabId}UploadArea`), imageItems);
      }
    });
  }
  
  // Initialize action buttons
  function initializeActionButtons(getSelectedCompressionLevel) {
    // Handle clear button
    const clearButtons = document.querySelectorAll('.clear-button');
    clearButtons.forEach(button => {
      button.addEventListener('click', () => {
        const container = button.closest('.loaded-files-container');
        if (container) {
          // Get tab ID from container ID
          const tabId = container.id.replace('LoadedFiles', '');
          
          // Clear the files array for this tab
          loadedFiles[tabId] = [];
          
          // Reset file input
          const fileInput = document.getElementById(`${tabId}FileInput`);
          if (fileInput) {
            fileInput.value = '';
          }
          
          // Update UI
          updateLoadedFilesUI(tabId);
        }
      });
    });
    
    // Initialize start button
    const startButton = document.getElementById('startHalftoneButton');
    if (startButton) {
      startButton.addEventListener('click', async () => {
        try {
        const files = loadedFiles['halftone'];
        if (files.length === 0) {
          showNotification('Please add some files first', 'warning');
          return;
        }
        
        // Select save location first
        selectedSaveLocation = await selectSaveLocation();
        if (selectedSaveLocation === null && 'showDirectoryPicker' in window) {
          // User cancelled directory selection when API is available
          showNotification('Processing cancelled. Please select a save location.', 'warning');
          return;
        }
        
        // Start processing
        isProcessing = true;
        shouldStopProcessing = false;
        currentAbortController = new AbortController();
        const abortSignal = currentAbortController.signal;
        
        // Update UI to processing state
        document.getElementById('halftoneLoadedFiles').style.display = 'none';
        document.getElementById('startHalftoneButton').style.display = 'none';
        document.querySelector('.compression-option').style.display = 'none';
        
        // Show processing container and button
        const processingContainer = document.getElementById('halftoneProcessingFiles');
        processingContainer.style.display = 'flex';
        
        // Show and setup processing button with the new structure
        const processingButton = document.getElementById('processingHalftoneButton');
        processingButton.style.display = 'flex';
        processingButton.disabled = false; // Make sure it's not disabled
        processingButton.classList.remove('stopping'); // Remove any stopping class
        processingButton.querySelector('.stop-content span').textContent = 'Stop Processing'; // Reset text if needed
        
        // Initial progress values
        const counter = processingContainer.querySelector('.files-counter');
        const progressText = processingContainer.querySelector('.progress-text');
        const progressFill = processingContainer.querySelector('.progress-fill');
        const processingHint = document.getElementById('processingHint');
        
        // Save the original processing hint text
        const originalHintText = processingHint.textContent;
        
        counter.textContent = `0 of ${files.length}`;
        progressText.textContent = '0%';
        progressFill.style.width = '0%';
        
        // Process each file
        let successCount = 0;
        
        for (let i = 0; i < files.length; i++) {
          // Stop if requested
          if (shouldStopProcessing) {
            break;
          }
          
          try {
            // Update progress for current file
            counter.textContent = `${i + 1} of ${files.length}`;
            const totalProgress = Math.round(((i) / files.length) * 100);
            progressText.textContent = `${totalProgress}%`;
            progressFill.style.width = `${totalProgress}%`;
            
            // Keep the original processing hint text
            // Just update the console for debugging purposes
            console.log(`Processing ${files[i].name}...`);
            
            const processedBlob = await window.halftoneProcessor.processHalftoneImage(
              files[i],
              (progress) => {
                // Only log progress to console, don't update the hint text
                const stageText = {
                  'loading': 'Loading image',
                  'preparing': 'Preparing canvas',
                  'enhancing': 'Enhancing colors',
                  'grayscale': 'Converting to grayscale',
                  'levels': 'Adjusting levels',
                  'halftone': 'Creating halftone pattern',
                  'dots': 'Drawing dots',
                  'masking': 'Applying color mask',
                  'finalizing': 'Finalizing image',
                  'compression': 'Compressing image',
                  'complete': 'Complete'
                };
                
                console.log(`${stageText[progress.stage] || 'Processing'} ${files[i].name}... (${progress.progress}%)`);
              },
              abortSignal
            );
            
            // Apply compression if needed (only for files >10MB)
            console.log(`Finalizing ${files[i].name}...`);
            const finalBlob = await window.halftoneProcessor.applyAdaptiveCompression(processedBlob, applyCompression, abortSignal);
            
            // Save the processed file
            console.log(`Saving ${files[i].name}...`);
            const saved = await saveProcessedFile(finalBlob, files[i].name, selectedSaveLocation);
            
            if (saved) {
              successCount++;
              console.log(`Saved ${files[i].name} successfully.`);
            } else {
              console.log(`Failed to save ${files[i].name}.`);
            }
            
            // Brief pause between files
            // await new Promise(resolve => setTimeout(resolve, 500));
            
          } catch (error) {
            console.error(`Error processing file ${files[i].name}:`, error);
            // Don't update the processing hint text, just log to console
            
            // Brief pause to show error
            // await new Promise(resolve => setTimeout(resolve, 1000));
            if (shouldStopProcessing) {
              break;
            }
          }
          
          // Update overall progress
          const totalProgress = Math.round(((i + 1) / files.length) * 100);
          progressText.textContent = `${totalProgress}%`;
          progressFill.style.width = `${totalProgress}%`;

          // Wait for 3 seconds after processing each file
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        // Processing complete
        isProcessing = false;
        currentAbortController = null;
        
        // Reset UI
        processingContainer.style.display = 'none';
        processingButton.style.display = 'none';
        
        // Show success notification
        if (shouldStopProcessing) {
          showNotification(`Processing stopped. ${successCount} of ${files.length} files processed.`, 'warning');
        } else {
          showNotification(`Successfully processed ${successCount} of ${files.length} files!`, 'success');
        }
        
        // Clear files and show upload area again
        resetUIToOriginalState('halftone');
        } catch (error) {
          if (error && error.name === 'AbortError') {
            shouldStopProcessing = true;
            showNotification('Processing stopped by user.', 'warning');
          } else {
            console.error('Halftone processing run failed:', error);
            showNotification('An error occurred during processing.', 'warning');
          }
        } finally {
          isProcessing = false;
          if (currentAbortController) { try { currentAbortController.abort(); } catch(e) {} }
          currentAbortController = null;
          const processingContainer = document.getElementById('halftoneProcessingFiles');
          const processingButton = document.getElementById('processingHalftoneButton');
          if (processingButton) {
            processingButton.classList.remove('stopping');
            processingButton.disabled = false;
            processingButton.style.display = 'none';
            const btnText = processingButton.querySelector('.stop-content span');
            if (btnText) btnText.textContent = 'Stop Processing';
          }
          if (processingContainer) processingContainer.style.display = 'none';
          resetUIToOriginalState('halftone');
        }
      });
    }
    
    // Initialize processing buttons
    const processingButtons = {
      halftone: document.getElementById('processingHalftoneButton'),
      compression: document.getElementById('processingCompressionButton')
    };
    
    Object.entries(processingButtons).forEach(([type, button]) => {
      if (button) {
        // Click handler to stop processing
        button.addEventListener('click', () => {
          if (isProcessing) {
            // Stop processing
            shouldStopProcessing = true;
            if (currentAbortController) {
              try { currentAbortController.abort(); } catch (e) {}
            }
            
            // Update UI to show stopping state
            button.querySelector('.stop-content span').textContent = 'Stopping...';
            
            // Disable button by adding a class
            button.classList.add('stopping');
            button.style.backgroundColor = '#888';
            button.style.cursor = 'not-allowed';
            button.disabled = true;
          }
        });
      }
    });
    
    // Initialize compression start button
    const startCompressionButton = document.getElementById('startCompressionButton');
    if (startCompressionButton) {
      startCompressionButton.addEventListener('click', async () => {
        const files = loadedFiles['compression'];
        if (files.length === 0) {
          showNotification('Please add some files first', 'warning');
          return;
        }
        
        // Select save location first
        selectedSaveLocation = await selectSaveLocation();
        if (selectedSaveLocation === null && 'showDirectoryPicker' in window) {
          // User cancelled directory selection when API is available
          showNotification('Processing cancelled. Please select a save location.', 'warning');
          return;
        }
        
        // Start processing
        isProcessing = true;
        shouldStopProcessing = false;
        
        // Update UI to processing state
        document.getElementById('compressionLoadedFiles').style.display = 'none';
        document.getElementById('startCompressionButton').style.display = 'none';
        document.querySelector('.compression-options').style.display = 'none';
        
        // Show processing container and button
        const processingContainer = document.getElementById('compressionProcessingFiles');
        processingContainer.style.display = 'flex';
        
        // Show and setup processing button
        const processingButton = document.getElementById('processingCompressionButton');
        processingButton.style.display = 'flex';
        processingButton.disabled = false;
        processingButton.classList.remove('stopping');
        processingButton.querySelector('.stop-content span').textContent = 'Stop Processing';
        
        // Initial progress values
        const counter = processingContainer.querySelector('.files-counter');
        const progressText = processingContainer.querySelector('.progress-text');
        const progressFill = processingContainer.querySelector('.progress-fill');
        const processingHint = document.getElementById('compressionProcessingHint');
        
        counter.textContent = `0 of ${files.length}`;
        progressText.textContent = '0%';
        progressFill.style.width = '0%';
        
        // Get selected compression level
        const compressionLevel = getSelectedCompressionLevel();
        
        // Process each file
        let successCount = 0;
        
        for (let i = 0; i < files.length; i++) {
          // Stop if requested
          if (shouldStopProcessing) {
            break;
          }
          
          try {
            // Update progress for current file
            counter.textContent = `${i + 1} of ${files.length}`;
            const totalProgress = Math.round(((i) / files.length) * 100);
            progressText.textContent = `${totalProgress}%`;
            progressFill.style.width = `${totalProgress}%`;
            
            console.log(`Processing ${files[i].name}...`);
            
            const processedBlob = await window.compressionProcessor.processImage(
              files[i],
              compressionLevel,
              (progress) => {
                console.log(`${progress.stage} ${files[i].name}... (${progress.progress}%)`);
              }
            );
            
            // Save the processed file
            console.log(`Saving ${files[i].name}...`);
            const saved = await saveProcessedFile(processedBlob, files[i].name, selectedSaveLocation);
            
            if (saved) {
              successCount++;
              console.log(`Saved ${files[i].name} successfully.`);
            } else {
              console.log(`Failed to save ${files[i].name}.`);
            }
            
          } catch (error) {
            console.error(`Error processing file ${files[i].name}:`, error);
          }
          
          // Update overall progress
          const totalProgress = Math.round(((i + 1) / files.length) * 100);
          progressText.textContent = `${totalProgress}%`;
          progressFill.style.width = `${totalProgress}%`;

          // Wait for 3 seconds after processing each file
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        // Processing complete
        isProcessing = false;
        
        // Reset UI
        processingContainer.style.display = 'none';
        processingButton.style.display = 'none';
        
        // Show success notification
        if (shouldStopProcessing) {
          showNotification(`Processing stopped. ${successCount} of ${files.length} files processed.`, 'warning');
        } else {
          showNotification(`Successfully processed ${successCount} of ${files.length} files!`, 'success');
        }
        
        // Clear files and show upload area again
        resetUIToOriginalState('compression');
      });
    }
  }
  
  // Reset UI to original state (like fresh page load)
  function resetUIToOriginalState(tabId) {
    console.log(`Resetting UI to original state for ${tabId} tab`);
    
    // 1. Clear files array
    loadedFiles[tabId] = [];
    
    // 2. Reset file input values
    const fileInput = document.getElementById(`${tabId}FileInput`);
    if (fileInput) {
      fileInput.value = '';
    }
    
    // 3. Reset processing state variables
    isProcessing = false;
    shouldStopProcessing = false;
    selectedSaveLocation = null;
    
    // 4. Reset checkbox states to initial values
    if (tabId === 'halftone') {
      // Reset halftone compression checkbox to unchecked
      applyCompression = false;
      const compressionCheckbox = document.getElementById('compressionCheckbox');
      if (compressionCheckbox) {
        const checkboxIcon = compressionCheckbox.querySelector('.checkbox-icon');
        if (checkboxIcon) {
          checkboxIcon.src = './assets/uncheckedbox-ic.svg';
        }
      }
    } else if (tabId === 'compression') {
      // Reset compression options to adaptive (default)
      const compressionOptions = [
        { id: 'adaptiveCompressionCheckbox', level: 'adaptive', isDefault: true },
        { id: 'lightCompressionCheckbox', level: 'light', isDefault: false },
        { id: 'balancedCompressionCheckbox', level: 'balanced', isDefault: false },
        { id: 'aggressiveCompressionCheckbox', level: 'aggressive', isDefault: false }
      ];
      
      compressionOptions.forEach(option => {
        const checkbox = document.getElementById(option.id);
        if (checkbox) {
          const checkboxIcon = checkbox.querySelector('img');
          if (checkboxIcon) {
            checkboxIcon.src = `./assets/${option.isDefault ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
          }
        }
      });
    }
    
    // 5. Update UI containers to show correct state
    updateLoadedFilesUI(tabId);
    
    // 6. Ensure drag-drop area is visible and in correct state
    const uploadArea = document.getElementById(`${tabId}UploadArea`);
    if (uploadArea) {
      uploadArea.style.display = 'flex';
      uploadArea.classList.remove('drag-active', 'success', 'error', 'processing');
    }
    
    // 7. Hide processing containers
    const processingContainer = document.getElementById(`${tabId}ProcessingFiles`);
    if (processingContainer) {
      processingContainer.style.display = 'none';
    }
    
    // 8. Hide processing buttons
    const processingButton = document.getElementById(`processing${tabId.charAt(0).toUpperCase() + tabId.slice(1)}Button`);
    if (processingButton) {
      processingButton.style.display = 'none';
      processingButton.disabled = false;
      processingButton.classList.remove('stopping');
      const buttonText = processingButton.querySelector('.stop-content span');
      if (buttonText) {
        buttonText.textContent = 'Stop Processing';
      }
    }
    
    // 9. Hide action buttons (they will be shown when files are loaded)
    const actionButton = document.getElementById(tabId === 'halftone' ? 'startHalftoneButton' : 'startCompressionButton');
    if (actionButton) {
      actionButton.style.display = 'none';
    }
    
    // 10. Reset any drag-drop area styling
    if (uploadArea) {
      uploadArea.style.borderColor = '';
      uploadArea.style.backgroundColor = '';
    }
    
    console.log(`UI reset complete for ${tabId} tab`);
  }
  
  // Process and filter files
  function processFiles(area, files) {
    const tabId = area.id.replace('UploadArea', '');

    // Filter for valid image files
    const validTypes = tabId === 'halftone'
      ? ['image/png', 'image/jpeg', 'image/webp']
      : tabId === 'vectorize'
      ? ['image/png', 'image/jpeg', 'image/webp']
      : ['image/png'];

    const acceptedFiles = [];
    const unsupportedFiles = [];

    Array.from(files).forEach(file => {
      if (file.type.match('image.*') && validTypes.some(type => file.type.includes(type))) {
        acceptedFiles.push(file);
      } else {
        unsupportedFiles.push(file);
      }
    });

    // Filter out duplicate files from acceptedFiles
    const uniqueFiles = acceptedFiles.filter(newFile => {
      return !loadedFiles[tabId].some(existingFile => 
        existingFile.name === newFile.name && 
        existingFile.size === newFile.size && 
        existingFile.lastModified === newFile.lastModified
      );
    });

    // Show combined alert if both accepted and unsupported files exist
    if (uniqueFiles.length > 0 && unsupportedFiles.length > 0) {
      showNotification(`${uniqueFiles.length} file${uniqueFiles.length > 1 ? 's' : ''} added. ${unsupportedFiles.length} unsupported file${unsupportedFiles.length > 1 ? 's were' : ' was'} skipped.`, 'warning');
    } else if (uniqueFiles.length > 0) {
      showNotification(`${uniqueFiles.length} file${uniqueFiles.length > 1 ? 's' : ''} added successfully!`, 'success');
    } else if (unsupportedFiles.length > 0) {
      if (tabId === 'compression') {
        showNotification('Some files are not supported. Please upload PNG images only.', 'warning');
      } else {
        showNotification('Some files are not supported. Please upload valid image files.', 'warning');
      }
    }

    // If no unique files after filtering, exit
    if (uniqueFiles.length === 0) {
      return;
    }

    // Add only unique files to existing files array
    loadedFiles[tabId] = [...loadedFiles[tabId], ...uniqueFiles];

    // Update UI with the new files
    updateLoadedFilesUI(tabId);

    // Make sure appropriate action button is visible based on tab
    if (tabId === 'vectorize') {
      // Special handling for vectorize tab
      showVectorizeInterface();
      if (uniqueFiles.length > 0) {
        loadImageToVectorizeCanvas(uniqueFiles[0]);
      }
    } else {
      const actionButton = document.getElementById(tabId === 'halftone' ? 'startHalftoneButton' : 'startCompressionButton');
      if (actionButton) {
        actionButton.style.display = 'flex';
      }
    }

    // Show success state briefly
    area.classList.add('success');
    setTimeout(() => {
      area.classList.remove('success');
    }, 2000);
  }
  
  // Update the UI based on loaded files
  function updateLoadedFilesUI(tabId) {
    const files = loadedFiles[tabId];
    const loadedFilesContainer = document.getElementById(`${tabId}LoadedFiles`);
    const uploadArea = document.getElementById(`${tabId}UploadArea`);
    const processingContainer = document.getElementById(`${tabId}ProcessingFiles`);
    
    // Hide processing container if it exists
    if (processingContainer) {
      processingContainer.style.display = 'none';
    }
    
    if (files.length > 0) {
      // Show loaded files container
      if (loadedFilesContainer) {
        loadedFilesContainer.style.display = 'flex';
        loadedFilesContainer.classList.add('visible');
        
        // Update counter
        const counter = loadedFilesContainer.querySelector('.files-counter');
        if (counter) {
          counter.textContent = `${files.length} of 100`;
        }
        
        // Update progress text
        const progressText = loadedFilesContainer.querySelector('.progress-text');
        if (progressText) {
          const percentage = Math.min(Math.round((files.length / 100) * 100), 100);
          progressText.textContent = `${percentage}% of max files`;
        }
        
        // Update progress bar
        const progressFill = loadedFilesContainer.querySelector('.progress-fill');
        if (progressFill) {
          const percentage = Math.min(files.length / 100 * 100, 100);
          progressFill.style.width = `${percentage}%`;
        }
      }
      
      // Hide the drag and drop area
      if (uploadArea) {
        uploadArea.style.display = 'none';
      }
      
      // Show action button based on tab
      const actionButton = document.getElementById(tabId === 'halftone' ? 'startHalftoneButton' : 'startCompressionButton');
      if (actionButton) {
        actionButton.style.display = 'flex';
      }
      
      // Show compression options based on tab
      if (tabId === 'halftone') {
        const compressionOption = document.querySelector('.compression-option');
        if (compressionOption) {
          compressionOption.style.display = 'flex';
        }
      } else if (tabId === 'compression') {
        const compressionOptions = document.querySelector('.compression-options');
        if (compressionOptions) {
          compressionOptions.style.display = 'flex';
        }
      }
    } else {
      // No files, hide loaded files container and show upload area
      if (loadedFilesContainer) {
        loadedFilesContainer.style.display = 'none';
        loadedFilesContainer.classList.remove('visible');
      }
      
      if (uploadArea) {
        uploadArea.style.display = 'flex';
      }
      
      // Hide action button
      const actionButton = document.getElementById(tabId === 'halftone' ? 'startHalftoneButton' : 'startCompressionButton');
      if (actionButton) {
        actionButton.style.display = 'none';
      }
      
      // Hide compression options
      if (tabId === 'halftone') {
        const compressionOption = document.querySelector('.compression-option');
        if (compressionOption) {
          compressionOption.style.display = 'none';
        }
      } else if (tabId === 'compression') {
        const compressionOptions = document.querySelector('.compression-options');
        if (compressionOptions) {
          compressionOptions.style.display = 'none';
        }
      }
    }
  }
  
  // Select save location
  async function selectSaveLocation() {
    // Try to use the File System Access API if available
    if ('showDirectoryPicker' in window) {
      try {
        // Use { mode: 'readwrite' } to request permission once
        const dirHandle = await window.showDirectoryPicker({ 
          mode: 'readwrite',
          startIn: 'downloads' // Suggest downloads folder as starting point
        });
        
        // Verify we have permission
        const options = { mode: 'readwrite' };
        const permissionState = await dirHandle.queryPermission(options);
        
        if (permissionState !== 'granted') {
          const newPermissionState = await dirHandle.requestPermission(options);
          if (newPermissionState !== 'granted') {
            showNotification('Permission to save files was denied.', 'warning');
            return null;
          }
        }
        
        return dirHandle;
      } catch (error) {
        console.error('Error selecting directory:', error);
        // Fall back to basic method if user cancelled or API failed
        return null;
      }
    } else {
      // No directory picker API available, let user know files will download normally
      showNotification('Your browser does not support folder selection. Files will download automatically.', 'warning');
      return null;
    }
  }
  
  // Save file to selected location or download
  async function saveProcessedFile(blob, fileName, directoryHandle) {
    try {
      // Ensure the file has .png extension
      const baseName = fileName.replace(/\.[^/.]+$/, ""); // Remove any extension
      const pngFileName = `${baseName}.png`;
      
      if (directoryHandle) {
        // Use File System Access API
        const fileHandle = await directoryHandle.getFileHandle(pngFileName, { create: true });
        const writable = await fileHandle.createWritable();
        await writable.write(blob);
        await writable.close();
        return true;
      } else {
        // Fallback to standard download - avoid Chrome confirmation dialogs
        // by creating a temporary anchor that auto-clicks
        return new Promise(resolve => {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = pngFileName;
          a.style.display = 'none';
          document.body.appendChild(a);
          
          // Add a small timeout to ensure browser doesn't batch downloads
          setTimeout(() => {
            a.click();
            // Clean up
            setTimeout(() => {
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
              resolve(true);
            }, 100);
          }, 50);
        });
      }
    } catch (error) {
      console.error('Error saving file:', error);
      return false;
    }
  }
  
  console.log('Snap Image Studio component initialization complete');
}

// Create a reusable function for showing notifications
function showNotification(message, type = 'warning') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  
  // Append to main-content instead of body
  const mainContent = document.querySelector('.main-content');
  if (mainContent) {
    mainContent.appendChild(notification);
  } else {
    document.body.appendChild(notification);
  }
  
  // Remove notification after 3 seconds
  setTimeout(() => {
    notification.remove();
  }, 3000);
  
  return notification;
}

// Tip card data
const tipCards = [
  {
    title: 'Start with High-Resolution Images',
    description: 'Upload your design at 4K resolution (4096×4096px or higher). High-res images ensure the halftone effect is crisp, detailed, and print-ready for professional results.',
    showPrev: false,
    showNext: true,
    showDone: false
  },
  {
    title: 'Only Black Backgrounds Supported',
    description: 'Halftone separation works only on designs with black backgrounds. It is optimized for black shirts but may also suit navy or dark heather grey, depending on the design.',
    showPrev: true,
    showNext: true,
    showDone: false
  },
  {
    title: 'Ready for Merch on Demand',
    description: 'The final image will be exported at 4500×5400 pixels which is the standard size for Merch on Demand. No resizing needed, just upload your processed file directly to MOD platform.',
    showPrev: true,
    showNext: false,
    showDone: true
  }
];

let currentTipIndex = 0;
let doNotShowAgainChecked = false;

function showTipCard() {
  // Check if tip card should be shown
  const shouldShowTip = localStorage.getItem('showHalftoneTip') !== 'false';
  if (!shouldShowTip) return;

  // Remove any existing tip card
  const mainContent = document.querySelector('.main-content');
  if (!mainContent) return;
  const existingTipCard = mainContent.querySelector('.tip-card');
  if (existingTipCard) existingTipCard.remove();

  const tipCard = document.createElement('div');
  tipCard.className = 'tip-card';
  
  const currentTip = tipCards[currentTipIndex];
  
  tipCard.innerHTML = `
    <div class="tip-card-content">
      <div class="tip-card-title">${currentTip.title}</div>
      <div class="tip-card-description">${currentTip.description}</div>
    </div>
    <div class="tip-card-footer">
      <div class="checkbox-wrapper" id="tipCheckbox">
        <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" class="checkbox-icon">
        <label>Do not show again</label>
      </div>
      <div class="tip-card-navigation">
        ${currentTip.showPrev ? '<button class="prev">Prev</button>' : ''}
        ${currentTip.showNext ? '<button class="next">Next</button>' : ''}
        ${currentTip.showDone ? '<button class="done">Done</button>' : ''}
      </div>
    </div>
  `;

  // Append to main-content (Snap Image Studio area)
  mainContent.appendChild(tipCard);

  // Setup event listeners
  const checkboxWrapper = tipCard.querySelector('#tipCheckbox');
  const checkboxIcon = checkboxWrapper.querySelector('.checkbox-icon');
  const checkboxLabel = checkboxWrapper.querySelector('label');
  
  // Set checkbox icon based on persistent state
  checkboxIcon.src = `./assets/${doNotShowAgainChecked ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;

  // Handle checkbox click on wrapper, icon, and label
  const toggleCheckbox = () => {
    doNotShowAgainChecked = !doNotShowAgainChecked;
    checkboxIcon.src = `./assets/${doNotShowAgainChecked ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
  };

  checkboxWrapper.addEventListener('click', toggleCheckbox);
  checkboxIcon.addEventListener('click', (e) => {
    e.stopPropagation();
    toggleCheckbox();
  });
  checkboxLabel.addEventListener('click', (e) => {
    e.stopPropagation();
    toggleCheckbox();
  });

  // Navigation buttons
  const prevButton = tipCard.querySelector('button.prev');
  const nextButton = tipCard.querySelector('button.next');
  const doneButton = tipCard.querySelector('button.done');

  if (prevButton) {
    prevButton.addEventListener('click', () => {
      if (currentTipIndex > 0) {
        currentTipIndex--;
        tipCard.style.opacity = '0';
        tipCard.style.transform = 'translateX(20px)';
        setTimeout(() => {
          showTipCard();
        }, 300);
      }
    });
  }

  if (nextButton) {
    nextButton.addEventListener('click', () => {
      if (currentTipIndex < tipCards.length - 1) {
        currentTipIndex++;
        tipCard.style.opacity = '0';
        tipCard.style.transform = 'translateX(-20px)';
        setTimeout(() => {
          showTipCard();
        }, 300);
      }
    });
  }

  if (doneButton) {
    doneButton.addEventListener('click', () => {
      if (doNotShowAgainChecked) {
        localStorage.setItem('showHalftoneTip', 'false');
      }
      hideTipCard();
    });
  }

  // Ensure the new card is visible with a nice transition
  requestAnimationFrame(() => {
    tipCard.style.opacity = '1';
    tipCard.style.transform = 'translateX(0)';
  });
}

function hideTipCard() {
  const mainContent = document.querySelector('.main-content');
  if (!mainContent) return;
  const tipCard = mainContent.querySelector('.tip-card');
  if (tipCard) {
    tipCard.remove();
  }
}

// Export component data
window.snapImageStudioComponent = {
  render: initSnapImageStudio,
  html: snapImageStudioHTML,
  css: snapImageStudioCSS
}; 